/**
 * اختبار حفظ أمر التوريد بنفس الرقم المعروض
 */

console.log('🧪 اختبار حفظ أمر التوريد برقم محدد...\n');

async function testSaveSupplyOrderWithSpecificId() {
  try {
    const supplyOrderData = {
      supplyOrderId: 'SUP-1', // رقم محدد
      supplierId: 1,
      warehouseId: 1,
      employeeName: 'اختبار',
      invoiceNumber: 'INV-001',
      supplyDate: new Date().toISOString(),
      notes: 'اختبار حفظ بنفس الرقم',
      items: [
        {
          imei: 'TEST123456789',
          deviceModel: 'iPhone Test',
          condition: 'جديد',
          price: 1000,
          notes: 'جهاز اختبار'
        }
      ],
      status: 'completed'
    };

    console.log('📤 إرسال طلب إنشاء أمر توريد برقم:', supplyOrderData.supplyOrderId);
    console.log('📊 البيانات:', JSON.stringify(supplyOrderData, null, 2));

    const response = await fetch('http://localhost:9005/api/supply', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(supplyOrderData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ تم إنشاء أمر التوريد بنجاح!');
      console.log('📊 النتيجة:', result);
      console.log('🔍 رقم الأمر المحفوظ:', result.supplyOrderId);
      
      if (result.supplyOrderId === supplyOrderData.supplyOrderId) {
        console.log('🎯 ممتاز! تم الحفظ بنفس الرقم المطلوب');
      } else {
        console.log('⚠️  تحذير: تم الحفظ برقم مختلف');
        console.log(`المطلوب: ${supplyOrderData.supplyOrderId}`);
        console.log(`المحفوظ: ${result.supplyOrderId}`);
      }
    } else {
      console.log('❌ فشل في إنشاء أمر التوريد');
      console.log('🔍 السبب:', result);
    }

  } catch (error) {
    console.error('💥 خطأ في الطلب:', error.message);
  }
}

// اختبار مع رقم غير موجود
async function testWithNewNumber() {
  try {
    const timestamp = Date.now();
    const supplyOrderData = {
      supplyOrderId: `SUP-${timestamp}`, // رقم مضمون أن يكون فريد
      supplierId: 1,
      warehouseId: 1,
      employeeName: 'اختبار',
      supplyDate: new Date().toISOString(),
      items: [
        {
          imei: `TEST${timestamp}`,
          deviceModel: 'iPhone Test 2',
          condition: 'جديد',
          price: 1200
        }
      ],
      status: 'completed'
    };

    console.log(`\n📤 اختبار برقم فريد: ${supplyOrderData.supplyOrderId}`);

    const response = await fetch('http://localhost:9005/api/supply', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(supplyOrderData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ تم إنشاء أمر التوريد بنجاح!');
      console.log('🔍 رقم الأمر المحفوظ:', result.supplyOrderId);
      
      if (result.supplyOrderId === supplyOrderData.supplyOrderId) {
        console.log('🎯 ممتاز! تم الحفظ بنفس الرقم المطلوب');
      } else {
        console.log('⚠️  تحذير: تم الحفظ برقم مختلف');
      }
    } else {
      console.log('❌ فشل في إنشاء أمر التوريد');
      console.log('🔍 السبب:', result);
    }

  } catch (error) {
    console.error('💥 خطأ في الطلب:', error.message);
  }
}

// تشغيل الاختبارات
testSaveSupplyOrderWithSpecificId()
  .then(() => testWithNewNumber())
  .then(() => {
    console.log('\n🎯 انتهاء اختبار أرقام أوامر التوريد');
  });
