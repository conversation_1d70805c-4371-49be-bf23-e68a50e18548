// اختبار سريع لرفع المرفقات في التوريد
console.log('🧪 اختبار رفع المرفقات مع التفويض المحدث...');

// محاكاة المستخدم
const mockUser = { name: 'admin', role: 'admin' };

// إنشاء التفويض كما في المتجر
const createAuthHeader = (user) => {
  const token = btoa(`user:${user.name}:${user.role}`);
  return { 'Authorization': `Bearer ${token}` };
};

async function testUploadWithNewAuth() {
  try {
    // إنشاء ملف تجريبي
    const testFileContent = new Blob(['اختبار المرفقات بعد التحديث'], { type: 'text/plain' });
    const testFile = new File([testFileContent], 'test-after-fix.txt', { type: 'text/plain' });

    // إنشاء FormData
    const formData = new FormData();
    formData.append('files', testFile);
    formData.append('section', 'supply');

    // إنشاء التفويض
    const authHeaders = createAuthHeader(mockUser);

    console.log('📤 محاولة رفع الملف مع التفويض الجديد...');
    console.log('🔐 Headers:', authHeaders);

    // محاولة رفع الملف
    const response = await fetch('http://localhost:9005/api/upload', {
      method: 'POST',
      headers: authHeaders,
      body: formData,
    });

    const result = await response.json();

    if (response.ok && result.success) {
      console.log('✅ نجح رفع الملف!');
      console.log('📝 الرسالة:', result.message);
      console.log('📁 الملفات:', result.files.length);
      
      result.files.forEach((file, index) => {
        console.log(`  📄 الملف ${index + 1}: ${file.originalName} -> ${file.fileName}`);
      });

      console.log('\n🎉 تم إصلاح مشكلة رفع المرفقات بنجاح!');
      console.log('   ✅ التفويض يعمل بشكل صحيح');
      console.log('   ✅ قسم "supply" مدعوم');
      console.log('   ✅ يمكن الآن رفع المرفقات في أوامر التوريد');
      
    } else {
      console.log('❌ فشل في رفع الملف');
      console.log('❌ الحالة:', response.status);
      console.log('❌ الخطأ:', result.error);
      
      if (result.error && result.error.includes('authorization')) {
        console.log('🚨 لا يزال هناك مشكلة في التفويض');
      }
    }

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

// تشغيل الاختبار
testUploadWithNewAuth();
