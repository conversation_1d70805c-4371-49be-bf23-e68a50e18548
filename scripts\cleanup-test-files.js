const fs = require('fs');
const path = require('path');

// حذف ملفات الاختبار
const testFiles = [
  'test-upload-fix.js',
  'test-upload-with-auth.js',
  'test-deletion-fix.js',
  'test-deletion-fix-simple.js'
];

console.log('تنظيف ملفات الاختبار...');

testFiles.forEach(file => {
  try {
    if (fs.existsSync(file)) {
      fs.unlinkSync(file);
      console.log(`✅ تم حذف ${file}`);
    }
  } catch (error) {
    console.log(`❌ خطأ في حذف ${file}:`, error.message);
  }
});

console.log('✅ تم الانتهاء من تنظيف ملفات الاختبار');
