/**
 * اختبار إضافة مورد بدون بريد إلكتروني
 */

console.log('🧪 اختبار إضافة مورد جديد...\n');

async function testAddSupplier() {
  try {
    // بيانات المورد بدون بريد إلكتروني
    const supplierData = {
      name: 'مورد تجريبي',
      phone: '0123456789',
      email: '' // بريد إلكتروني فارغ
    };

    console.log('📤 إرسال طلب إضافة مورد:', supplierData);

    const response = await fetch('http://localhost:9005/api/suppliers', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer admin_token' // استخدم token صحيح عند الحاجة
      },
      body: JSON.stringify(supplierData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ تم إضافة المورد بنجاح!');
      console.log('📊 النتيجة:', result);
    } else {
      console.log('❌ فشل في إضافة المورد');
      console.log('🔍 السبب:', result);
    }

  } catch (error) {
    console.error('💥 خطأ في الطلب:', error.message);
  }
}

// اختبار بريد إلكتروني صالح
async function testAddSupplierWithEmail() {
  try {
    const supplierData = {
      name: 'مورد بإيميل',
      phone: '0987654321',
      email: '<EMAIL>'
    };

    console.log('\n📤 إرسال طلب إضافة مورد مع بريد إلكتروني:', supplierData);

    const response = await fetch('http://localhost:9005/api/suppliers', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(supplierData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ تم إضافة المورد مع البريد الإلكتروني بنجاح!');
      console.log('📊 النتيجة:', result);
    } else {
      console.log('❌ فشل في إضافة المورد');
      console.log('🔍 السبب:', result);
    }

  } catch (error) {
    console.error('💥 خطأ في الطلب:', error.message);
  }
}

// تشغيل الاختبارات
testAddSupplier().then(() => {
  return testAddSupplierWithEmail();
}).then(() => {
  console.log('\n🎯 انتهاء الاختبارات');
});
