// Script to update deleteSupplyOrder function to use API endpoints

const fs = require('fs');
const path = require('path');

// Define the updated function
const deleteSupplyOrderFix = `  const deleteSupplyOrder = async (orderId: number) => {
    try {
      // التحقق من العلاقات أولاً
      const relationCheck = checkSupplyOrderRelations(orderId);
      if (!relationCheck.canDelete) {
        throw new Error(
          \`لا يمكن حذف أمر التوريد: \${relationCheck.reason}\${relationCheck.relatedOperations ? "\\nالعمليات المرتبطة: " + relationCheck.relatedOperations.join(", ") : ""}\`,
        );
      }

      const orderToDelete = supplyOrders.find((o) => o.id === orderId);
      if (!orderToDelete) return;

      // إرسال طلب الحذف إلى API
      const response = await fetch(\`/api/supply/\${orderId}\`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete supply order");
      }

      // تحديث واجهة المستخدم
      
      // Remove associated devices from inventory
      const imeisToRemove = orderToDelete.items.map((item) => item.imei);
      setDevices((prevDevices) =>
        prevDevices.filter((device) => !imeisToRemove.includes(device.id)),
      );

      // Remove the supply order
      setSupplyOrders((prevOrders) => prevOrders.filter((o) => o.id !== orderId));

      const supplier = suppliers.find((s) => s.id === orderToDelete.supplierId);
      addActivity({
        type: "supply",
        description: \`تم حذف أمر التوريد \${orderToDelete.supplyOrderId} و \${imeisToRemove.length} أجهزة من المورد \${supplier?.name || "غير معروف"}\`,
      });
    } catch (error) {
      console.error("Failed to delete supply order:", error);
      throw error;
    }
  };`;

// Read the store.tsx file
const filePath = path.join(__dirname, 'context', 'store.tsx');
let content = fs.readFileSync(filePath, 'utf8');

// Replace the function
content = content.replace(/const deleteSupplyOrder = \(orderId: number\) => \{[\s\S]*?description: `تم حذف أمر التوريد \${orderToDelete\.supplyOrderId} و \${imeisToRemove\.length} أجهزة من المورد \${supplier\?\.name \|\| "غير معروف"}`[\s\S]*?\}\);(\s*\}\;)/s, deleteSupplyOrderFix);

// Write the updated content back to the file
fs.writeFileSync(filePath, content);
console.log('deleteSupplyOrder function updated successfully to use API endpoints.');
