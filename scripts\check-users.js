const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUsers() {
  console.log('🔍 فحص المستخدمين الموجودين في قاعدة البيانات...');
  
  try {
    // جلب جميع المستخدمين
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        username: true,
        email: true,
        role: true,
        status: true,
        permissions: true,
        createdAt: true
      }
    });

    console.log(`\n📊 إجمالي المستخدمين: ${users.length}`);
    
    if (users.length === 0) {
      console.log('❌ لا يوجد مستخدمين في قاعدة البيانات');
      return;
    }

    console.log('\n👥 قائمة المستخدمين:');
    console.log('='.repeat(80));
    
    users.forEach((user, index) => {
      console.log(`\n${index + 1}. المستخدم:`);
      console.log(`   🆔 ID: ${user.id}`);
      console.log(`   👤 الاسم: ${user.name || 'غير محدد'}`);
      console.log(`   🔑 اسم المستخدم: ${user.username || 'غير محدد'}`);
      console.log(`   📧 البريد الإلكتروني: ${user.email}`);
      console.log(`   🎭 الدور: ${user.role || 'غير محدد'}`);
      console.log(`   📊 الحالة: ${user.status || 'غير محدد'}`);
      console.log(`   📅 تاريخ الإنشاء: ${user.createdAt}`);
      
      // فحص الصلاحيات
      if (user.permissions) {
        const permissions = typeof user.permissions === 'string' 
          ? JSON.parse(user.permissions) 
          : user.permissions;
        
        const permissionCount = Object.keys(permissions).length;
        console.log(`   🔐 عدد الصلاحيات: ${permissionCount}`);
        
        // عرض بعض الصلاحيات المهمة
        const importantPerms = ['dashboard', 'users', 'inventory', 'sales'];
        const hasImportantPerms = importantPerms.filter(perm => 
          permissions[perm] && permissions[perm].view
        );
        console.log(`   ✅ الصلاحيات المهمة: ${hasImportantPerms.join(', ') || 'لا توجد'}`);
      } else {
        console.log(`   🔐 الصلاحيات: غير محددة`);
      }
    });

    // البحث عن مستخدم admin
    const adminUser = users.find(user => 
      user.role === 'admin' || 
      user.username === 'admin' || 
      user.email?.includes('admin')
    );

    if (adminUser) {
      console.log('\n🎯 مستخدم الإدارة موجود:');
      console.log(`   👤 ${adminUser.name} (${adminUser.username})`);
      console.log(`   📧 ${adminUser.email}`);
      console.log(`   🎭 ${adminUser.role}`);
    } else {
      console.log('\n⚠️  لم يتم العثور على مستخدم إدارة!');
    }

  } catch (error) {
    console.error('❌ خطأ في فحص المستخدمين:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUsers();
