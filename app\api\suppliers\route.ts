import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, checkRelationsBeforeDelete } from '@/lib/transaction-utils';
import {
  extractApiQueryParams,
  paginationToPrisma,
  sortToPrisma,
  searchToPrisma,
  filtersToPrisma,
  createPaginatedResponse,
  validatePaginationParams,
  validateSortParams
} from '@/lib/api-helpers';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // استخراج معاملات API
    const allowedFilters = ['name', 'phone', 'email', 'address', 'status'];
    const queryParams = extractApiQueryParams(request, allowedFilters);

    // التحقق من صحة المعاملات
    if (queryParams.pagination) {
      const paginationErrors = validatePaginationParams(queryParams.pagination);
      if (paginationErrors.length > 0) {
        return NextResponse.json({ error: paginationErrors.join(', ') }, { status: 400 });
      }
    }

    const allowedSortFields = ['id', 'name', 'phone', 'email', 'createdAt'];
    if (queryParams.sort) {
      const sortErrors = validateSortParams(queryParams.sort, allowedSortFields);
      if (sortErrors.length > 0) {
        return NextResponse.json({ error: sortErrors.join(', ') }, { status: 400 });
      }
    }

    // بناء شروط Prisma
    const paginationPrisma = paginationToPrisma(queryParams.pagination || {});
    const sortPrisma = sortToPrisma(queryParams.sort, allowedSortFields);
    const searchPrisma = searchToPrisma(queryParams.search, ['name', 'phone', 'email', 'address']);

    // معالجة الفلاتر المخصصة
    const filtersPrisma = filtersToPrisma(queryParams.filters || {});

    // دمج شروط البحث والتصفية
    const whereClause = {
      ...filtersPrisma,
      ...(searchPrisma && { ...searchPrisma })
    };

    // جلب العدد الإجمالي
    const total = await prisma.supplier.count({ where: whereClause });

    // جلب البيانات
    const suppliers = await prisma.supplier.findMany({
      where: whereClause,
      ...paginationPrisma,
      orderBy: sortPrisma || { id: 'desc' }
    });

    // إنشاء الاستجابة المرقمة
    const response = createPaginatedResponse(suppliers, total, queryParams.pagination || {}, queryParams);

    return NextResponse.json(response);
  } catch (error) {
    console.error('Failed to fetch suppliers:', error);
    return NextResponse.json({ error: 'Failed to fetch suppliers' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newSupplier = await request.json();

    // التحقق من البيانات المطلوبة
    if (!newSupplier.name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من عدم وجود بريد إلكتروني مكرر (إذا تم توفيره)
      if (newSupplier.email && newSupplier.email.trim()) {
        const existingSupplier = await tx.supplier.findFirst({
          where: { 
            email: newSupplier.email
          }
        });

        if (existingSupplier) {
          throw new Error('Email already exists');
        }
      }

      // إنشاء المورد في قاعدة البيانات
      const supplier = await tx.supplier.create({
        data: {
          name: newSupplier.name,
          phone: newSupplier.phone || '',
          email: newSupplier.email || '',
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created supplier: ${supplier.name}`,
        tableName: 'supplier',
        recordId: supplier.id
      });

      return supplier;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Failed to create supplier:', error);

    if (error instanceof Error && error.message === 'Email already exists') {
      return NextResponse.json({ error: 'Email already exists' }, { status: 409 });
    }

    return NextResponse.json({ error: 'Failed to create supplier' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedSupplier = await request.json();

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من وجود المورد
      const existingSupplier = await tx.supplier.findUnique({
        where: { id: updatedSupplier.id }
      });

      if (!existingSupplier) {
        throw new Error('Supplier not found');
      }

      // التحقق من عدم وجود بريد إلكتروني مكرر (باستثناء المورد الحالي)
      if (updatedSupplier.email && updatedSupplier.email.trim() && updatedSupplier.email !== existingSupplier.email) {
        const emailExists = await tx.supplier.findUnique({
          where: { email: updatedSupplier.email }
        });

        if (emailExists) {
          throw new Error('Email already exists');
        }
      }

      // تحديث المورد
      const supplier = await tx.supplier.update({
        where: { id: updatedSupplier.id },
        data: {
          name: updatedSupplier.name,
          phone: updatedSupplier.phone || '',
          email: updatedSupplier.email || '',
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated supplier: ${supplier.name}`,
        tableName: 'supplier',
        recordId: supplier.id
      });

      return supplier;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update supplier:', error);

    if (error instanceof Error) {
      if (error.message === 'Supplier not found') {
        return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
      }
      if (error.message === 'Email already exists') {
        return NextResponse.json({ error: 'Email already exists' }, { status: 409 });
      }
    }

    return NextResponse.json({ error: 'Failed to update supplier' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من وجود المورد
      const existingSupplier = await tx.supplier.findUnique({
        where: { id }
      });

      if (!existingSupplier) {
        throw new Error('Supplier not found');
      }

      // فحص العلاقات قبل الحذف
      const relationCheck = await checkRelationsBeforeDelete(tx, 'supplier', id);

      // فحص إضافي لأوامر التوريد
      const relatedSupplyOrders = await tx.supplyOrder?.findMany({
        where: { supplierId: id }
      }) || [];

      const relatedOperations: string[] = [...relationCheck.relations];
      if (relatedSupplyOrders.length > 0) {
        relatedOperations.push(`${relatedSupplyOrders.length} أمر توريد`);
      }

      if (relatedOperations.length > 0) {
        throw new Error(`Cannot delete supplier: ${relatedOperations.join(', ')}`);
      }

      // حذف المورد
      await tx.supplier.delete({
        where: { id }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted supplier: ${existingSupplier.name}`,
        tableName: 'supplier',
        recordId: id
      });

      return { message: 'Supplier deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete supplier:', error);

    if (error instanceof Error) {
      if (error.message === 'Supplier not found') {
        return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
      }
      if (error.message.startsWith('Cannot delete supplier:')) {
        const relatedOperations = error.message.replace('Cannot delete supplier: ', '').split(', ');
        return NextResponse.json({
          error: 'Cannot delete supplier',
          reason: 'يوجد عمليات مرتبطة بهذا المورد',
          relatedOperations
        }, { status: 409 });
      }
    }

    return NextResponse.json({ error: 'Failed to delete supplier' }, { status: 500 });
  }
}
