/* تنسيقات تقرير تتبع الجهاز */

.device-tracking-report {
  font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  max-width: 210mm; /* A4 width */
  margin: 0 auto;
  background: white;
  min-height: 297mm; /* A4 height */
}

/* تنسيقات المحتوى */
.report-content {
  padding: 20px;
  margin: 20px 0;
}

/* تنسيقات العناوين */
.report-content h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
}

/* تنسيقات عناصر المعلومات */
.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background: #f9fafb;
  border-radius: 6px;
  border-right: 3px solid #3b82f6;
}

.info-item span:first-child {
  font-weight: 600;
  color: #374151;
  min-width: 120px;
}

.info-item span:last-child {
  color: #111827;
  flex: 1;
}

/* تنسيقات الخط الزمني */
.timeline {
  position: relative;
  padding-right: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  right: 1rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
  padding-right: 3rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  right: 0.75rem;
  top: 0.5rem;
  width: 12px;
  height: 12px;
  background: #3b82f6;
  border: 3px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  z-index: 1;
}

.timeline-item:last-child::after {
  content: '';
  position: absolute;
  right: 0.5rem;
  bottom: -1rem;
  width: 20px;
  height: 20px;
  background: #10b981;
  border: 4px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* تنسيقات محتوى الأحداث */
.timeline-item .bg-gray-50 {
  background: #f8fafc !important;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.timeline-item .bg-gray-50:hover {
  background: #f1f5f9 !important;
  box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

/* تنسيقات الشارات */
.timeline-item .bg-blue-100 {
  background: #dbeafe !important;
  color: #1e40af !important;
  font-weight: 500;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

/* تنسيقات الطباعة */
@media print {
  .device-tracking-report {
    font-size: 12pt;
    line-height: 1.4;
    color: black;
    background: white;
    box-shadow: none;
    margin: 0;
    padding: 0;
    max-width: none;
    min-height: auto;
  }

  .report-content {
    padding: 15mm;
    margin: 0;
  }

  .report-content h3 {
    font-size: 14pt;
    font-weight: bold;
    color: black;
    page-break-after: avoid;
    margin-bottom: 8pt;
    padding-bottom: 4pt;
    border-bottom: 1pt solid black;
  }

  .info-item {
    background: transparent !important;
    border: none !important;
    margin-bottom: 4pt;
    padding: 2pt 0;
    page-break-inside: avoid;
  }

  .info-item span:first-child {
    font-weight: bold;
    color: black;
  }

  .info-item span:last-child {
    color: black;
  }

  .timeline::before {
    background: black !important;
    width: 1pt;
  }

  .timeline-item::before {
    background: black !important;
    border-color: white !important;
    width: 8pt;
    height: 8pt;
  }

  .timeline-item:last-child::after {
    background: black !important;
    border-color: white !important;
    width: 12pt;
    height: 12pt;
  }

  .timeline-item .bg-gray-50 {
    background: transparent !important;
    border: 1pt solid #ccc !important;
    box-shadow: none !important;
    page-break-inside: avoid;
  }

  .timeline-item .bg-blue-100 {
    background: #f0f0f0 !important;
    color: black !important;
    border: 1pt solid #ccc !important;
  }

  /* تجنب كسر الصفحة داخل العناصر المهمة */
  .device-basic-info,
  .sale-info,
  .warranty-info {
    page-break-inside: avoid;
  }

  .timeline-item {
    page-break-inside: avoid;
    margin-bottom: 12pt;
  }

  /* إخفاء العناصر غير المطلوبة في الطباعة */
  .no-print {
    display: none !important;
  }
}

/* تنسيقات متجاوبة */
@media (max-width: 768px) {
  .device-tracking-report {
    margin: 0;
    padding: 0;
    max-width: 100%;
  }

  .report-content {
    padding: 15px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    text-align: right;
  }

  .info-item span:first-child {
    min-width: auto;
    margin-bottom: 0.25rem;
  }

  .timeline {
    padding-right: 1.5rem;
  }

  .timeline-item {
    padding-right: 2rem;
  }
}

/* تحسينات للطباعة بالأبيض والأسود */
@media print and (monochrome) {
  .device-tracking-report {
    color: black !important;
  }

  .report-content h3 {
    color: black !important;
    border-bottom-color: black !important;
  }

  .info-item {
    background: white !important;
    border-right-color: black !important;
  }

  .timeline::before {
    background: black !important;
  }

  .timeline-item::before,
  .timeline-item:last-child::after {
    background: black !important;
    border-color: white !important;
  }

  .timeline-item .bg-gray-50 {
    background: white !important;
    border-color: black !important;
  }

  .timeline-item .bg-blue-100 {
    background: #f5f5f5 !important;
    color: black !important;
    border: 1pt solid black !important;
  }
}

/* تنسيقات خاصة للعرض على الشاشة */
@media screen {
  .device-tracking-report {
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
  }

  .report-content {
    background: white;
  }

  /* تأثيرات تفاعلية */
  .info-item:hover {
    background: #f3f4f6;
    transform: translateX(-2px);
    transition: all 0.2s ease;
  }

  .timeline-item:hover .bg-gray-50 {
    transform: translateX(-4px);
    transition: all 0.2s ease;
  }
}

/* تنسيقات الحالات الخاصة */
.warranty-active {
  color: #059669 !important;
  font-weight: 600;
}

.warranty-expired {
  color: #dc2626 !important;
  font-weight: 600;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-available {
  background: #d1fae5;
  color: #065f46;
}

.status-sold {
  background: #fef3c7;
  color: #92400e;
}

.status-maintenance {
  background: #fecaca;
  color: #991b1b;
}

.status-returned {
  background: #e0e7ff;
  color: #3730a3;
}
