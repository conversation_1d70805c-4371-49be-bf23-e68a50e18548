// اختبار شامل لجميع APIs
const userToken = Buffer.from('user:admin:admin').toString('base64');

async function testAllAPIs() {
  console.log('🧪 اختبار شامل لجميع APIs...\n');

  const apis = [
    { name: 'Users', endpoint: '/api/users', method: 'GET' },
    { name: 'Settings', endpoint: '/api/settings', method: 'GET' },
    { name: 'Clients', endpoint: '/api/clients', method: 'GET' },
    { name: 'Supply Orders', endpoint: '/api/supply', method: 'GET' },
    { name: 'Warehouses', endpoint: '/api/warehouses', method: 'GET' },
    { name: 'Suppliers', endpoint: '/api/suppliers', method: 'GET' },
    { name: 'Device Tracking', endpoint: '/api/device-tracking', method: 'GET' },
    { name: 'Maintenance Orders', endpoint: '/api/maintenance-orders', method: 'GET' },
    { name: 'Delivery Orders', endpoint: '/api/delivery-orders', method: 'GET' },
    { name: 'Database Connections', endpoint: '/api/database/connections', method: 'GET' }
  ];

  for (const api of apis) {
    try {
      const response = await fetch(`http://localhost:9005${api.endpoint}`, {
        method: api.method,
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      });

      const status = response.ok ? '✅' : '❌';
      console.log(`${status} ${api.name}: ${response.status} ${response.statusText}`);
      
      if (!response.ok && response.status !== 404) {
        const error = await response.text();
        console.log(`   خطأ: ${error.substring(0, 100)}...`);
      }
      
    } catch (error) {
      console.log(`❌ ${api.name}: خطأ في الاتصال - ${error.message}`);
    }
  }

  console.log('\n🎉 انتهى الاختبار الشامل!');
}

testAllAPIs();
