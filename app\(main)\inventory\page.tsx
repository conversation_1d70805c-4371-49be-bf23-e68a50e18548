
'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import {
  format,
  subDays,
  startOfDay,
  endOfDay,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
} from 'date-fns';
import Link from 'next/link';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import type { Device, DeviceStatus, SystemSettings, ApiQueryParams, PaginatedResponse } from '@/lib/types';
import { PaginatedTable } from '@/components/ui/paginated-table';
import { SearchInput, AdvancedSearchInput } from '@/components/ui/search-input';
import { LoadingSpinner, InlineLoading } from '@/components/ui/loading-spinner';
import { AdvancedFilters, FilterConfig, FilterValues } from '@/components/ui/advanced-filters';

// UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, <PERSON>Footer, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Calendar as CalendarIcon,
  Eye,
  Filter,
  ChevronsUpDown,
  Check,
  Printer,
  FileDown,
  FileSpreadsheet,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as XLSX from 'xlsx';

type ModelSummary = {
  model: string;
  total: number;
  available: number;
  maintenance: number;
  inRepair: number;
  sold: number;
  defective: number;
  damaged: number;
  // New fields for detailed evaluations
  defectiveDevices: Device[];
  damagedDevices: Device[];
  maintenanceDevices: Device[];
  inRepairDevices: Device[];
  soldDevices: Device[];
  availableDevices: Device[];
  // New fields for device condition
  newDevices: Device[];
  usedDevices: Device[];
};

export default function InventoryPage() {
  // Get data fetching functions and store data
  const {
    fetchDevicesData,
    fetchWarehousesData,
    fetchSuppliersData,
    manufacturers,
    suppliers,
    warehouses,
    returns
  } = useStore();

  const { toast } = useToast();

  // State management for filters and pagination
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [warehouseFilter, setWarehouseFilter] = useState<string>('');
  const [supplierFilter, setSupplierFilter] = useState<string>('');
  const [manufacturerFilter, setManufacturerFilter] = useState<string>('');
  const [returnedOnlyFilter, setReturnedOnlyFilter] = useState<boolean>(false);
  const [dateRange, setDateRange] = useState<string>('all');
  const [customDate, setCustomDate] = useState<Date | undefined>(undefined);
  const [selectedModel, setSelectedModel] = useState<string>('all');
  const [showTotalOnly, setShowTotalOnly] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [allDevicesSearchQuery, setAllDevicesSearchQuery] = useState<string>('');
  const [allDevicesStatusFilter, setAllDevicesStatusFilter] = useState<string>('all');
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [deviceTrackingDetails, setDeviceTrackingDetails] = useState<Device | null>(null);
  const [isDeviceTrackingModalOpen, setIsDeviceTrackingModalOpen] = useState<boolean>(false);

  // State management for pagination and filtering
  const [isLoading, setIsLoading] = useState(false);
  const [devicesData, setDevicesData] = useState<PaginatedResponse<Device> | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterValues, setFilterValues] = useState<FilterValues>({});
  const [sortField, setSortField] = useState('dateAdded');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Modal states
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [detailsModel, setDetailsModel] = useState<ModelSummary | null>(null);
  const [isDeviceDetailsModalOpen, setIsDeviceDetailsModalOpen] = useState(false);
  const [deviceDetails, setDeviceDetails] = useState<{
    title: string;
    devices: Device[];
  } | null>(null);

  // Filter configuration for advanced filters
  const filterConfigs: FilterConfig[] = [
    {
      key: 'status',
      label: 'حالة الجهاز',
      type: 'multiselect',
      options: [
        { value: 'متاح للبيع', label: 'متاح للبيع' },
        { value: 'بانتظار إرسال للصيانة', label: 'بانتظار إرسال للصيانة' },
        { value: 'بانتظار استلام في الصيانة', label: 'بانتظار استلام في الصيانة' },
        { value: 'قيد الإصلاح', label: 'قيد الإصلاح' },
        { value: 'بانتظار تسليم من الصيانة', label: 'بانتظار تسليم من الصيانة' },
        { value: 'مباع', label: 'مباع' },
        { value: 'معيب', label: 'معيب' },
        { value: 'تالف', label: 'تالف' }
      ]
    },
    {
      key: 'condition',
      label: 'حالة الجهاز',
      type: 'select',
      options: [
        { value: 'جديد', label: 'جديد' },
        { value: 'مستخدم', label: 'مستخدم' }
      ]
    },
    {
      key: 'warehouseId',
      label: 'المخزن',
      type: 'select',
      options: warehouses.map(w => ({ value: w.id.toString(), label: w.name }))
    },
    {
      key: 'supplierId',
      label: 'المورد',
      type: 'select',
      options: suppliers.map(s => ({ value: s.id.toString(), label: s.name }))
    },
    {
      key: 'priceRange',
      label: 'نطاق السعر',
      type: 'range',
      min: 0,
      max: 10000,
      step: 100
    },
    {
      key: 'dateRange',
      label: 'تاريخ الإضافة',
      type: 'daterange'
    }
  ];

  // Fetch devices data with pagination and filters
  const fetchDevices = useCallback(async (params: ApiQueryParams): Promise<PaginatedResponse<Device>> => {
    setIsLoading(true);
    try {
      // Build filters from filterValues and search query
      const filters: Record<string, any> = {};

      // Add search query
      if (searchQuery.trim()) {
        filters.search = searchQuery;
      }

      // Add filter values
      Object.entries(filterValues).forEach(([key, value]) => {
        if (value && (Array.isArray(value) ? value.length > 0 : true)) {
          if (key === 'priceRange' && Array.isArray(value)) {
            filters.priceMin = value[0];
            filters.priceMax = value[1];
          } else if (key === 'dateRange' && typeof value === 'object' && value.from && value.to) {
            filters.dateFrom = value.from;
            filters.dateTo = value.to;
          } else {
            filters[key] = value;
          }
        }
      });

      const queryParams: ApiQueryParams = {
        ...params,
        filters
      };

      const result = await fetchDevicesData(queryParams);
      return result;
    } catch (error) {
      console.error('خطأ في تحميل بيانات الأجهزة:', error);
      toast({
        variant: "destructive",
        title: "خطأ في تحميل البيانات",
        description: "الرجاء المحاولة مرة أخرى"
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [fetchDevicesData, searchQuery, filterValues, toast]);

  // Define table columns for devices
  const deviceColumns = [
    {
      key: 'id',
      title: 'رقم الجهاز',
      sortable: true,
      searchable: true,
      width: 'w-32',
      render: (device: Device) => (
        <div className="font-mono text-sm">{device.id}</div>
      )
    },
    {
      key: 'model',
      title: 'الموديل',
      sortable: true,
      searchable: true,
      render: (device: Device) => (
        <div className="font-medium">{device.model}</div>
      )
    },
    {
      key: 'storage',
      title: 'التخزين',
      render: (device: Device) => (
        <Badge variant="outline">{device.storage}</Badge>
      )
    },
    {
      key: 'condition',
      title: 'الحالة',
      render: (device: Device) => (
        <Badge variant={device.condition === 'جديد' ? 'default' : 'secondary'}>
          {device.condition}
        </Badge>
      )
    },
    {
      key: 'status',
      title: 'الحالة',
      render: (device: Device) => {
        const statusColors = {
          'متاح للبيع': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100',
          'بانتظار إرسال للصيانة': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100',
          'بانتظار استلام في الصيانة': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100',
          'قيد الإصلاح': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100',
          'بانتظار تسليم من الصيانة': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100',
          'مباع': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-100',
          'معيب': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100',
          'تالف': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100'
        };
        return (
          <Badge
            variant="outline"
            className={statusColors[device.status as keyof typeof statusColors] || ''}
          >
            {device.status}
          </Badge>
        );
      }
    },
    {
      key: 'price',
      title: 'السعر',
      sortable: true,
      render: (device: Device) => (
        <div className="font-medium">{device.price?.toLocaleString()} ر.س</div>
      )
    },
    {
      key: 'dateAdded',
      title: 'تاريخ الإضافة',
      sortable: true,
      render: (device: Device) => (
        <div className="text-sm text-muted-foreground">
          {format(new Date(device.dateAdded), 'yyyy/MM/dd')}
        </div>
      )
    },
    {
      key: 'actions',
      title: 'الإجراءات',
      render: (device: Device) => (
        <div className="flex gap-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => handleDeviceDetails(device)}
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  // Handle device details
  const handleDeviceDetails = (device: Device) => {
    setDeviceDetails({
      title: `تفاصيل الجهاز ${device.id}`,
      devices: [device]
    });
    setIsDeviceDetailsModalOpen(true);
  };

  const returnedDeviceIds = useMemo(
    () => new Set(returns.flatMap((r) => (Array.isArray(r.items) ? r.items : []).map((i) => i.deviceId))),
    [returns]
  );

  const manufacturerNames = useMemo(
    () =>
      (manufacturers || []).reduce(
        (acc, m) => {
          acc[m.id] = m.name;
          return acc;
        },
        {} as { [key: number]: string },
      ),
    [manufacturers]
    );

  const modelOptions = useMemo(() => {
    return [...new Set((devicesData?.data || []).map((d) => d.model))].sort();
  }, [devicesData?.data]);

  const baseFilteredDevices = useMemo(() => {
    let filtered = (devicesData?.data || []).filter((device) => {
      if (statusFilter.length > 0 && !statusFilter.includes(device.status)) return false;
      if (warehouseFilter && device.warehouseId?.toString() !== warehouseFilter) return false;
      if (supplierFilter && device.supplierId?.toString() !== supplierFilter) return false;
      if (manufacturerFilter) {
        const manuName = manufacturerNames[parseInt(manufacturerFilter, 10)];
        if (
          !manuName ||
          !device.model.toLowerCase().startsWith(manuName.toLowerCase())
        ) return false;
      }
      if (returnedOnlyFilter && !returnedDeviceIds.has(device.id)) return false;
      return true;
    });

    // Apply date range filter
    if (dateRange !== 'all') {
      const now = new Date();
      let startDate: Date | null = null;
      let endDate: Date | null = null;

      switch (dateRange) {
        case 'day':
          startDate = startOfDay(now);
          endDate = endOfDay(now);
          break;
        case 'threeDays':
          startDate = startOfDay(subDays(now, 2));
          endDate = endOfDay(now);
          break;
        case 'week':
          startDate = startOfDay(subDays(now, 6));
          endDate = endOfDay(now);
          break;
        case 'month':
          startDate = startOfMonth(now);
          endDate = endOfMonth(now);
          break;
        case 'threeMonths':
          startDate = startOfMonth(subDays(now, 90));
          endDate = endOfMonth(now);
          break;
        case 'sixMonths':
          startDate = startOfMonth(subDays(now, 180));
          endDate = endOfMonth(now);
          break;
        case 'year':
          startDate = startOfYear(now);
          endDate = endOfYear(now);
          break;
        case 'custom':
          if (customDate) {
            startDate = startOfDay(customDate);
            endDate = endOfDay(customDate);
          }
          break;
      }

      if (startDate && endDate) {
        filtered = filtered.filter((device) => {
          const deviceDate = new Date(device.dateAdded);
          return deviceDate >= startDate! && deviceDate <= endDate!;
        });
      }
    }

    return filtered;
  }, [
    devices,
    statusFilter,
    warehouseFilter,
    supplierFilter,
    manufacturerFilter,
    returnedOnlyFilter,
    returnedDeviceIds,
    manufacturerNames,
    dateRange,
    customDate,
  ]);

  const summaryData = useMemo((): ModelSummary[] => {
    const summary = baseFilteredDevices.reduce(
      (acc, device) => {
        if (!acc[device.model]) {
          acc[device.model] = {
            model: device.model,
            total: 0,
            available: 0,
            maintenance: 0,
            inRepair: 0,
            sold: 0,
            defective: 0,
            damaged: 0,
            defectiveDevices: [],
            damagedDevices: [],
            maintenanceDevices: [],
            inRepairDevices: [],
            soldDevices: [],
            availableDevices: [],
            newDevices: [],
            usedDevices: [],
          };
        }
        const modelSummary = acc[device.model];
        modelSummary.total++;
        
        // Process device condition (new or used)
        if (device.condition === 'جديد') {
          modelSummary.newDevices.push(device);
        } else if (device.condition === 'مستخدم') {
          modelSummary.usedDevices.push(device);
        }
        
        // Process device status
        switch (device.status) {
          case 'متاح للبيع':
            modelSummary.available++;
            modelSummary.availableDevices.push(device);
            break;
          case 'بانتظار إرسال للصيانة':
          case 'بانتظار استلام في الصيانة':
          case 'قيد الإصلاح':
          case 'بانتظار تسليم من الصيانة':
            modelSummary.maintenance++;
            modelSummary.maintenanceDevices.push(device);
            break;
          case 'قيد الإصلاح':
            modelSummary.inRepair++;
            modelSummary.inRepairDevices.push(device);
            break;
          case 'مباع':
            modelSummary.sold++;
            modelSummary.soldDevices.push(device);
            break;
          case 'معيب':
            modelSummary.defective++;
            modelSummary.defectiveDevices.push(device);
            break;
          case 'تالف':
            modelSummary.damaged++;
            modelSummary.damagedDevices.push(device);
            break;
        }
        return acc;
      },
      {} as Record<string, ModelSummary>
    );
    return Object.values(summary).sort((a, b) =>
      a.model.localeCompare(b.model)
    );
  }, [baseFilteredDevices]);

  const filteredSummaryData = useMemo(() => {
    if (selectedModel === 'all') {
      return summaryData;
    }
    return summaryData.filter((item) => item.model === selectedModel);
  }, [summaryData, selectedModel]);

  const handleStatusFilterChange = (status: string) => {
    // التعامل مع خيارات خاصة
    if (status === 'كل التقييمات') {
      setStatusFilter([
        'متاح للبيع',
        'تحتاج صيانة',
        'قيد الإصلاح',
        'مرسل للمخزن',
        'مباع',
        'معيب',
        'تالف',
      ]);
      setShowTotalOnly(false);
      setCurrentPage(1);
      return;
    }
    
    if (status === 'الإجمالي فقط') {
      // عند تفعيل "الإجمالي فقط"، نتأكد من إلغاء كل الخيارات الأخرى
      if (!showTotalOnly) {
        setStatusFilter([]);
      }
      setShowTotalOnly(!showTotalOnly);
      setCurrentPage(1);
      return;
    }
    
    // التعامل مع التقييمات العادية
    setStatusFilter((prev) =>
      prev.includes(status)
        ? prev.filter((s) => s !== status)
        : [...prev, status]
    );
    setCurrentPage(1); // إعادة تعيين رقم الصفحة عند تغيير الفلاتر
  };

  const clearFilters = () => {
    setSelectedModel('all');
    setStatusFilter([]);
    setShowTotalOnly(false);
    setWarehouseFilter('');
    setSupplierFilter('');
    setManufacturerFilter('');
    setReturnedOnlyFilter(false);
    setCurrentPage(1); // إعادة تعيين رقم الصفحة عند مسح الفلاتر
  };
  
  // إضافة وظيفة تحديث المخزون
  const handleRefresh = () => {
    setIsRefreshing(true);
    try {
      // تنفيذ إعادة حساب البيانات وتحديث العرض
      const updatedSummaryData = [...summaryData]; 
      
      toast({
        title: "تم التحديث بنجاح",
        description: "تم تحديث بيانات المخزون",
      });
      
      // تأخير زمني بسيط لمحاكاة التحديث
      setTimeout(() => {
        setIsRefreshing(false);
      }, 800);
      
    } catch (error) {
      toast({
        variant: "destructive",
        title: "خطأ في التحديث",
        description: "الرجاء المحاولة مرة أخرى"
      });
      setIsRefreshing(false);
    }
  };

  const handleOpenDetailsModal = (modelSummary: ModelSummary) => {
    setDetailsModel(modelSummary);
    setIsDetailsModalOpen(true);
  };

  const handleOpenDeviceDetailsModal = (title: string, devices: Device[]) => {
    setDeviceDetails({ title, devices });
    setIsDeviceDetailsModalOpen(true);
  };
  
  // إضافة وظيفة فتح نافذة تتبع الجهاز
  const handleOpenDeviceTrackingModal = (device: Device) => {
    setDeviceTrackingDetails(device);
    setIsDeviceTrackingModalOpen(true);
  };

  const getPdfHeaderFooter = (doc: jsPDF, settings: SystemSettings) => {
    const addHeader = () => {
      if (settings.logoUrl) {
        try {
          doc.addImage(settings.logoUrl, 'PNG', 15, 10, 20, 20);
        } catch (e) {
          console.error('Error adding logo image to PDF:', e);
        }
      }
      doc
        .setFontSize(16)
        .text(settings.companyName, 190, 15, { align: 'right' });
      doc
        .setFontSize(10)
        .text(settings.companyAddress, 190, 22, { align: 'right' });
      doc.text(settings.contactNumbers, 190, 29, { align: 'right' });
      doc.setLineWidth(0.5).line(15, 35, 195, 35);
    };
    const addFooter = (data: any) => {
      const pageCount = doc.internal.pages.length;
      doc
        .setFontSize(8)
        .text(
          `صفحة ${data.pageNumber} من ${pageCount - 1}`,
          data.settings.margin.left,
          doc.internal.pageSize.height - 10
    );
      if (settings.reportFooter) {
        doc.text(
          settings.reportFooter,
          195,
          doc.internal.pageSize.height - 10,
          { align: 'right' }
    );
      }
    };
    return { addHeader, addFooter };
  };

  const handleExport = (format: 'pdf' | 'excel') => {
    if (filteredSummaryData.length === 0) {
      toast({ variant: 'destructive', title: 'لا توجد بيانات للتصدير' });
      return;
    }

    const title = `تقرير المخزون ${selectedModel === 'all' ? 'الإجمالي' : selectedModel}`;
    if (format === 'pdf') {
      const doc = new jsPDF();
      doc.setR2L(true);
      const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);
      addHeader();
      doc.setFontSize(18).text(title, 190, 45, { align: 'right' });

      const head = [
        [
          'تالف',
          'معيب',
          'مباع',
          'قيد الإصلاح',
          'صيانة',
          'المتاح',
          'الإجمالي',
          'الموديل',
        ],
      ];
      const body = filteredSummaryData.map((item) => [
        item.damaged,
        item.defective,
        item.sold,
        item.inRepair,
        item.maintenance,
        item.available,
        item.total,
        item.model,
      ]);

      autoTable(doc, {
        head: head,
        body: body,
        startY: 55,
        styles: { font: 'Helvetica', halign: 'right' },
        headStyles: { halign: 'center', fillColor: [44, 51, 51] },
        columnStyles: {
          0: { halign: 'center' },
          1: { halign: 'center' },
          2: { halign: 'center' },
          3: { halign: 'center' },
          4: { halign: 'center' },
          5: { halign: 'center' },
          6: { halign: 'center' },
        },
        didDrawPage: addFooter,
      });
      doc.save(`inventory_report_${new Date().toISOString().slice(0, 10)}.pdf`);
    } else {
      const xlsxData = filteredSummaryData.map((item) => ({
        الموديل: item.model,
        الإجمالي: item.total,
        المتاح: item.available,
        صيانة: item.maintenance,
        'قيد الإصلاح': item.inRepair,
        مباع: item.sold,
        معيب: item.defective,
        تالف: item.damaged,
      }));
      const worksheet = XLSX.utils.json_to_sheet(xlsxData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'تقرير المخزون');
      XLSX.writeFile(
        workbook,
        `inventory_report_${new Date().toISOString().slice(0, 10)}.xlsx`
    );
    }
  };

  const handleDetailsModalExport = (action: 'print' | 'download') => {
    if (!detailsModel) return;

    const doc = new jsPDF();
    doc.setR2L(true);
    const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);
    addHeader();

    const title = `تقرير تفصيلي لموديل ${detailsModel.model}`;
    doc.setFontSize(18).text(title, 190, 45, { align: 'right' });

    const summaryBody = [
      ['الإجمالي', detailsModel.total],
      ['متاح للبيع', detailsModel.available],
      ['يحتاج صيانة', detailsModel.maintenance],
      ['قيد الإصلاح', detailsModel.inRepair],
      ['مباع', detailsModel.sold],
      ['معيب', detailsModel.defective],
      ['تالف', detailsModel.damaged],
    ];

    autoTable(doc, {
      startY: 55,
      head: [['الحالة', 'العدد']],
      body: summaryBody,
      styles: { font: 'Helvetica', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
      columnStyles: { 1: { halign: 'center' } },
      didDrawPage: addFooter,
    });

    if (action === 'print') {
      doc.output('dataurlnewwindow');
    } else {
      doc.save(
        `inventory_details_${detailsModel.model.replace(/\s+/g, '_')}.pdf`
    );
    }
  };
  
  // التعامل مع نافذة عرض كل الأجهزة
  const filteredAllDevices = useMemo(() => {
    let filtered = baseFilteredDevices;
    
    // تطبيق البحث
    if (allDevicesSearchQuery) {
      const query = allDevicesSearchQuery.toLowerCase();
      filtered = filtered.filter(device => 
        device.id.toLowerCase().includes(query) || 
        device.model.toLowerCase().includes(query)
      );
    }
    
    // تطبيق فلتر الحالة
    if (allDevicesStatusFilter !== "all") {
      filtered = filtered.filter(device => device.status === allDevicesStatusFilter);
    }
    
    return filtered;
  }, [baseFilteredDevices, allDevicesSearchQuery, allDevicesStatusFilter]);

  const totalInventory = useMemo(() => {
    // ✅ حساب إجمالي المخزون المتوفر فقط (بدون الأجهزة المباعة)
    return filteredSummaryData.reduce((sum, item) => sum + (item.total - item.sold), 0);
  }, [filteredSummaryData]);
  
  // حساب عدد الصفحات وعناصر الصفحة الحالية
  const totalPages = useMemo(() => {
    return Math.max(1, Math.ceil(filteredSummaryData.length / itemsPerPage));
  }, [filteredSummaryData, itemsPerPage]);
  
  // الموديلات المعروضة في الصفحة الحالية
  const currentPageItems = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredSummaryData.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredSummaryData, currentPage, itemsPerPage]);

  return (
    <div className="flex flex-col gap-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">إدارة المخزون</h1>
          <p className="text-muted-foreground">
            عرض وإدارة جميع الأجهزة في المخزون مع إمكانيات البحث والتصفية المتقدمة
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => handleExport('pdf')}>
            <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
          </Button>
          <Button variant="outline" onClick={() => handleExport('excel')}>
            <FileSpreadsheet className="ml-2 h-4 w-4" /> تصدير Excel
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">إجمالي الأجهزة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {devicesData?.pagination.total || 0}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              جميع الأجهزة في النظام
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">متاح للبيع</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {devicesData?.data.filter(d => d.status === 'متاح للبيع').length || 0}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              جاهز للبيع
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-950 dark:to-yellow-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">يحتاج صيانة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {devicesData?.data.filter(d =>
                d.status.includes('صيانة') || d.status.includes('إصلاح')
              ).length || 0}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              في الصيانة أو الإصلاح
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950 dark:to-red-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">معيب/تالف</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {devicesData?.data.filter(d =>
                d.status === 'معيب' || d.status === 'تالف'
              ).length || 0}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              يحتاج إصلاح أو استبدال
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Advanced Filters */}
      <AdvancedFilters
        filters={filterConfigs}
        values={filterValues}
        onChange={setFilterValues}
        onReset={() => setFilterValues({})}
        title="فلاتر البحث المتقدمة"
        description="استخدم الفلاتر لتضييق نطاق البحث في الأجهزة"
        collapsible={true}
        defaultExpanded={false}
        showActiveCount={true}
      />

      {/* Main Devices Table */}
      <PaginatedTable
        title="قائمة الأجهزة"
        description="جميع الأجهزة المتوفرة في المخزون مع إمكانيات البحث والتصفية"
        columns={deviceColumns}
        fetchData={fetchDevices}
        searchPlaceholder="البحث في الأجهزة (رقم الجهاز، الموديل، التخزين...)"
        defaultPageSize={20}
        defaultSort={{ field: 'dateAdded', direction: 'desc' }}
        onRowClick={handleDeviceDetails}
        emptyMessage="لا توجد أجهزة تطابق معايير البحث"
        className="min-h-[600px]"
      />

      {/* Device Details Modal */}
      <Dialog open={isDeviceDetailsModalOpen} onOpenChange={setIsDeviceDetailsModalOpen}>
        <DialogContent className="sm:max-w-4xl">
          <DialogHeader>
            <DialogTitle>{deviceDetails?.title}</DialogTitle>
            <DialogDescription>
              تفاصيل شاملة عن الجهاز المحدد
            </DialogDescription>
          </DialogHeader>

          {deviceDetails?.devices && deviceDetails.devices.length > 0 && (
            <div className="space-y-4">
              {deviceDetails.devices.map((device) => (
                <Card key={device.id}>
                  <CardHeader>
                    <CardTitle className="text-lg">جهاز رقم: {device.id}</CardTitle>
                  </CardHeader>
                  <CardContent className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">الموديل</Label>
                      <p className="text-sm text-muted-foreground">{device.model}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">التخزين</Label>
                      <p className="text-sm text-muted-foreground">{device.storage}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">الحالة</Label>
                      <Badge variant={device.condition === 'جديد' ? 'default' : 'secondary'}>
                        {device.condition}
                      </Badge>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">الحالة</Label>
                      <Badge variant="outline">{device.status}</Badge>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">السعر</Label>
                      <p className="text-sm text-muted-foreground">{device.price?.toLocaleString()} ر.س</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">تاريخ الإضافة</Label>
                      <p className="text-sm text-muted-foreground">
                        {format(new Date(device.dateAdded), 'yyyy/MM/dd HH:mm')}
                      </p>
                    </div>
                    {device.notes && (
                      <div className="col-span-2">
                        <Label className="text-sm font-medium">ملاحظات</Label>
                        <p className="text-sm text-muted-foreground">{device.notes}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">إغلاق</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
