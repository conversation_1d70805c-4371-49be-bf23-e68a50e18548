#!/usr/bin/env node

/**
 * Script للتحقق من أن جميع طلبات API تحتوي على headers التفويض المناسبة
 */

const fs = require('fs');
const path = require('path');

function checkAuthHeaders() {
  const filePath = 'context/store.tsx';
  const content = fs.readFileSync(filePath, 'utf8');
  
  // البحث عن جميع استخدامات fetch
  const fetchMatches = content.match(/fetch\s*\(\s*["'`][^"'`]*["'`]/g) || [];
  const apiClientMatches = content.match(/apiClient\.\w+\s*\(/g) || [];
  
  console.log('📊 تقرير فحص headers التفويض');
  console.log('=====================================');
  
  console.log(`🔍 وجدت ${fetchMatches.length} استخدام مباشر لـ fetch`);
  console.log(`✅ وجدت ${apiClientMatches.length} استخدام لـ apiClient`);
  
  if (fetchMatches.length > 0) {
    console.log('\n⚠️  طلبات fetch المباشرة (تحتاج إصلاح):');
    fetchMatches.forEach((match, index) => {
      console.log(`${index + 1}. ${match}`);
    });
  }
  
  if (apiClientMatches.length > 0) {
    console.log('\n✅ طلبات apiClient (مُصلحة):');
    apiClientMatches.forEach((match, index) => {
      console.log(`${index + 1}. ${match}`);
    });
  }
  
  // فحص وجود import لـ api-client
  const hasApiClientImport = content.includes('@/lib/api-client');
  console.log(`\n📦 API Client Import: ${hasApiClientImport ? '✅ موجود' : '❌ مفقود'}`);
  
  // فحص التطبيق العام
  const fixedFunctions = content.match(/apiClient\./g) || [];
  const totalFixes = fixedFunctions.length;
  
  console.log('\n📈 إحصائيات الإصلاح:');
  console.log(`✅ الوظائف المُصلحة: ${totalFixes}`);
  console.log(`⚠️  الوظائف التي تحتاج إصلاح: ${fetchMatches.length}`);
  
  const percentageFixed = totalFixes > 0 ? Math.round((totalFixes / (totalFixes + fetchMatches.length)) * 100) : 0;
  console.log(`📊 نسبة الإصلاح: ${percentageFixed}%`);
  
  if (fetchMatches.length === 0) {
    console.log('\n🎉 تهانينا! جميع طلبات API تحتوي على headers التفويض!');
    return true;
  } else {
    console.log('\n🔧 يحتاج مزيد من الإصلاح...');
    return false;
  }
}

// تشغيل الفحص
const result = checkAuthHeaders();
process.exit(result ? 0 : 1);

return (
    <StoreContext.Provider value={value}>
      {children}
    </StoreContext.Provider>
  );
}

export function useStore() {
  const context = useContext(StoreContext);
  // …باقي الكود
}
