// الوظائف المستخرجة من ملف maintenance-updated-functions.ts
// استخدم هذا الملف للنسخ واللصق في store.tsx

// ======= وظيفة addMaintenanceOrder =======
const addMaintenanceOrder = async (maintenanceOrder: MaintenanceOrder) => {
  try {
    setIsLoading(true);
    const response = await fetch('/api/maintenance-orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(maintenanceOrder),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'فشل في إضافة أمر الصيانة');
    }

    const savedOrder = await response.json();
    
    setMaintenanceOrders((prev) => {
      return [...prev, savedOrder];
    });
    
    return savedOrder;
  } catch (error) {
    console.error('خطأ في إضافة أمر صيانة:', error);
    throw error;
  } finally {
    setIsLoading(false);
  }
};

// ======= وظيفة updateMaintenanceOrder =======
const updateMaintenanceOrder = async (
  id: string,
  updatedMaintenanceOrder: MaintenanceOrder
) => {
  try {
    setIsLoading(true);
    const response = await fetch(`/api/maintenance-orders/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updatedMaintenanceOrder),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'فشل في تحديث أمر الصيانة');
    }

    const updatedOrder = await response.json();
    
    setMaintenanceOrders((prev) => {
      return prev.map((order) => {
        if (order.id === id) {
          return updatedOrder;
        }
        return order;
      });
    });
    
    return updatedOrder;
  } catch (error) {
    console.error('خطأ في تحديث أمر صيانة:', error);
    throw error;
  } finally {
    setIsLoading(false);
  }
};

// ======= وظيفة deleteMaintenanceOrder =======
const deleteMaintenanceOrder = async (id: string) => {
  try {
    setIsLoading(true);
    const response = await fetch(`/api/maintenance-orders/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'فشل في حذف أمر الصيانة');
    }

    setMaintenanceOrders((prev) => {
      return prev.filter((order) => order.id !== id);
    });
    
    return true;
  } catch (error) {
    console.error('خطأ في حذف أمر صيانة:', error);
    throw error;
  } finally {
    setIsLoading(false);
  }
};

// ======= وظيفة addMaintenanceReceiptOrder =======
const addMaintenanceReceiptOrder = async (receiptOrder: MaintenanceReceiptOrder) => {
  try {
    setIsLoading(true);
    const response = await fetch('/api/maintenance-receipts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(receiptOrder),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'فشل في إضافة أمر استلام الصيانة');
    }

    const savedOrder = await response.json();
    
    setMaintenanceReceiptOrders((prev) => {
      return [...prev, savedOrder];
    });
    
    return savedOrder;
  } catch (error) {
    console.error('خطأ في إضافة أمر استلام صيانة:', error);
    throw error;
  } finally {
    setIsLoading(false);
  }
};

// ======= وظيفة updateMaintenanceReceiptOrder =======
const updateMaintenanceReceiptOrder = async (
  id: string,
  updatedReceiptOrder: MaintenanceReceiptOrder
) => {
  try {
    setIsLoading(true);
    const response = await fetch(`/api/maintenance-receipts/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updatedReceiptOrder),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'فشل في تحديث أمر استلام الصيانة');
    }

    const updatedOrder = await response.json();
    
    setMaintenanceReceiptOrders((prev) => {
      return prev.map((order) => {
        if (order.id === id) {
          return updatedOrder;
        }
        return order;
      });
    });
    
    return updatedOrder;
  } catch (error) {
    console.error('خطأ في تحديث أمر استلام صيانة:', error);
    throw error;
  } finally {
    setIsLoading(false);
  }
};

// ======= وظيفة deleteMaintenanceReceiptOrder =======
const deleteMaintenanceReceiptOrder = async (id: string) => {
  try {
    setIsLoading(true);
    const response = await fetch(`/api/maintenance-receipts/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'فشل في حذف أمر استلام الصيانة');
    }

    setMaintenanceReceiptOrders((prev) => {
      return prev.filter((order) => order.id !== id);
    });
    
    return true;
  } catch (error) {
    console.error('خطأ في حذف أمر استلام صيانة:', error);
    throw error;
  } finally {
    setIsLoading(false);
  }
};

// ======= وظيفة addDeliveryOrder =======
const addDeliveryOrder = async (deliveryOrder: DeliveryOrder) => {
  try {
    setIsLoading(true);
    const response = await fetch('/api/delivery-orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(deliveryOrder),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'فشل في إضافة أمر التسليم');
    }

    const savedOrder = await response.json();
    
    setDeliveryOrders((prev) => {
      return [...prev, savedOrder];
    });
    
    return savedOrder;
  } catch (error) {
    console.error('خطأ في إضافة أمر تسليم:', error);
    throw error;
  } finally {
    setIsLoading(false);
  }
};

// ======= وظيفة updateDeliveryOrder =======
const updateDeliveryOrder = async (
  id: string,
  updatedDeliveryOrder: DeliveryOrder
) => {
  try {
    setIsLoading(true);
    const response = await fetch(`/api/delivery-orders/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updatedDeliveryOrder),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'فشل في تحديث أمر التسليم');
    }

    const updatedOrder = await response.json();
    
    setDeliveryOrders((prev) => {
      return prev.map((order) => {
        if (order.id === id) {
          return updatedOrder;
        }
        return order;
      });
    });
    
    return updatedOrder;
  } catch (error) {
    console.error('خطأ في تحديث أمر تسليم:', error);
    throw error;
  } finally {
    setIsLoading(false);
  }
};

// ======= وظيفة deleteDeliveryOrder =======
const deleteDeliveryOrder = async (id: string) => {
  try {
    setIsLoading(true);
    const response = await fetch(`/api/delivery-orders/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'فشل في حذف أمر التسليم');
    }

    setDeliveryOrders((prev) => {
      return prev.filter((order) => order.id !== id);
    });
    
    return true;
  } catch (error) {
    console.error('خطأ في حذف أمر تسليم:', error);
    throw error;
  } finally {
    setIsLoading(false);
  }
};

