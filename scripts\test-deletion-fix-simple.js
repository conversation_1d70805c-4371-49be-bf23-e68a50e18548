async function testDeletionFix() {
  try {
    console.log('Testing supply order deletion fix...');

    // 1. Create a test supply order with devices
    const createSupplyResponse = await fetch('http://localhost:9005/api/supply', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        supplyOrderId: 'TEST-DELETE-FIX-001',
        supplierId: 1, // assuming supplier with ID 1 exists
        warehouseId: 1, // assuming warehouse with ID 1 exists
        employeeName: 'Test Employee',
        invoiceNumber: 'TEST-INV-FIX-001',
        supplyDate: new Date().toISOString(),
        notes: 'Test order for deletion fix',
        items: [
          {
            imei: '123456789012346',
            manufacturer: 'Test Brand',
            model: 'Test Model',
            condition: 'جديد'
          }
        ],
        invoiceFileName: '',
        referenceNumber: 'TEST-REF-FIX-001',
        status: 'completed'
      })
    });

    if (!createSupplyResponse.ok) {
      const error = await createSupplyResponse.json();
      console.error('Failed to create test supply order:', error);
      return;
    }

    const createdOrder = await createSupplyResponse.json();
    console.log('✅ Created test supply order:', createdOrder.supplyOrderId);

    // 2. Verify the device was added to inventory
    const devicesResponse = await fetch('http://localhost:9005/api/devices');
    const devices = await devicesResponse.json();
    const testDevice = devices.find(d => d.id === '123456789012346');
    
    if (testDevice) {
      console.log('✅ Device was added to inventory:', testDevice.id);
    } else {
      console.log('❌ Device was not found in inventory');
      return;
    }

    // 3. Wait a moment
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 4. Delete the supply order
    const deleteResponse = await fetch('http://localhost:9005/api/supply', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id: createdOrder.id
      })
    });

    if (!deleteResponse.ok) {
      const error = await deleteResponse.json();
      console.error('Failed to delete supply order:', error);
      return;
    }

    console.log('✅ Successfully deleted supply order without errors');

    // 5. Verify the device was removed from inventory
    const updatedDevicesResponse = await fetch('http://localhost:9005/api/devices');
    const updatedDevices = await updatedDevicesResponse.json();
    const deletedDevice = updatedDevices.find(d => d.id === '123456789012346');
    
    if (!deletedDevice) {
      console.log('✅ Device was properly removed from inventory');
    } else {
      console.log('❌ Device still exists in inventory after deletion');
    }

    // 6. Verify the supply order was removed
    const ordersResponse = await fetch('http://localhost:9005/api/supply');
    const orders = await ordersResponse.json();
    const deletedOrder = orders.find(o => o.id === createdOrder.id);
    
    if (!deletedOrder) {
      console.log('✅ Supply order was properly removed');
    } else {
      console.log('❌ Supply order still exists after deletion');
    }

    console.log('\n🎉 Deletion fix test completed successfully!');
    console.log('   ✅ No "imeisToRemove is not defined" error occurred');
    console.log('   ✅ Supply order deleted from database');
    console.log('   ✅ Associated devices removed from inventory');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    
    if (error.message.includes('imeisToRemove is not defined')) {
      console.error('🚨 The fix did not resolve the imeisToRemove error!');
    }
  }
}

// Run the test
testDeletionFix();
