/**
 * اختبار حذف أمر التوريد وتحديث المخزون
 */

console.log('🧪 اختبار حذف أمر التوريد...\n');

// محاكاة بيانات الاختبار
const mockSupplyOrder = {
  id: 1,
  supplyOrderId: 'SUP-1',
  supplierId: 1,
  items: JSON.stringify([
    { imei: 'IMEI123', serialNumber: 'SN123', deviceModel: 'iPhone 13' },
    { imei: 'IMEI456', serialNumber: 'SN456', deviceModel: 'Galaxy S21' }
  ])
};

const mockDevices = [
  { id: 'IMEI123', model: 'iPhone 13', status: 'متوفر', storage: '128GB', price: 5000, condition: 'جديد', warehouseId: 1 },
  { id: 'IMEI456', model: 'Galaxy S21', status: 'متوفر', storage: '256GB', price: 4500, condition: 'جديد', warehouseId: 1 },
  { id: 'IMEI789', model: 'iPhone 14', status: 'متوفر', storage: '128GB', price: 6000, condition: 'جديد', warehouseId: 1 }
];

// محاكاة دالة حذف أمر التوريد
function simulateDeleteSupplyOrder(orderId) {
  console.log(`🗑️ محاولة حذف أمر التوريد رقم: ${orderId}`);
  
  const orderToDelete = mockSupplyOrder.id === orderId ? mockSupplyOrder : null;
  if (!orderToDelete) {
    console.log('❌ أمر التوريد غير موجود');
    return;
  }

  // تحليل العناصر
  let items = [];
  try {
    items = typeof orderToDelete.items === 'string' 
      ? JSON.parse(orderToDelete.items) 
      : Array.isArray(orderToDelete.items) 
        ? orderToDelete.items 
        : [];
  } catch (error) {
    console.error('❌ خطأ في تحليل عناصر أمر التوريد:', error);
    return;
  }

  const imeisToRemove = items.map((item) => item.imei).filter(Boolean);
  console.log('📱 الأجهزة المراد حذفها:', imeisToRemove);

  // عرض الأجهزة قبل الحذف
  console.log('\n📦 الأجهزة في المخزون قبل الحذف:');
  mockDevices.forEach(device => {
    console.log(`   - ${device.id}: ${device.model} (${device.status})`);
  });

  // محاكاة حذف الأجهزة من المخزون
  const remainingDevices = mockDevices.filter((device) => !imeisToRemove.includes(device.id));
  
  console.log('\n📦 الأجهزة في المخزون بعد الحذف:');
  remainingDevices.forEach(device => {
    console.log(`   - ${device.id}: ${device.model} (${device.status})`);
  });

  console.log(`\n✅ تم حذف أمر التوريد ${orderToDelete.supplyOrderId} و ${imeisToRemove.length} أجهزة من المخزون`);
  console.log(`📊 عدد الأجهزة المتبقية: ${remainingDevices.length} من أصل ${mockDevices.length}`);
}

// تشغيل الاختبار
simulateDeleteSupplyOrder(1);

console.log('\n🎯 خلاصة الاختبار:');
console.log('- ✅ تم تحليل عناصر أمر التوريد بنجاح');
console.log('- ✅ تم استخراج أرقام IMEI للأجهزة');
console.log('- ✅ تم حذف الأجهزة المرتبطة من المخزون');
console.log('- ✅ تم الاحتفاظ بالأجهزة غير المرتبطة');

console.log('\n✨ الاختبار مكتمل!');
