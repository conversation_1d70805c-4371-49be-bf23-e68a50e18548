// اختبار إنشاء قاعدة بيانات جديدة
const userToken = Buffer.from('user:admin:admin').toString('base64');

async function testCreateDatabase() {
  console.log('🧪 اختبار إنشاء قاعدة بيانات جديدة...\n');

  try {
    // 1. جلب الاتصالات أولاً
    console.log('1️⃣ جلب الاتصالات المتاحة...');
    const connectionsResponse = await fetch('http://localhost:9005/api/database/connections', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!connectionsResponse.ok) {
      throw new Error(`Failed to get connections: ${connectionsResponse.status}`);
    }

    const connectionsData = await connectionsResponse.json();
    const connections = connectionsData.connections || [];
    
    if (connections.length === 0) {
      throw new Error('No connections available');
    }

    const defaultConnection = connections.find(c => c.isDefault) || connections[0];
    console.log(`   ✅ سيتم استخدام الاتصال: ${defaultConnection.name}`);

    // 2. اختبار إنشاء قاعدة بيانات بأسماء مختلفة
    const testCases = [
      {
        name: 'قاعدة_بيانات_عربية', // اسم عربي (يجب أن يفشل)
        shouldFail: true,
        description: 'اسم عربي'
      },
      {
        name: 'test database', // مسافة (يجب أن يفشل)
        shouldFail: true,
        description: 'اسم بمسافة'
      },
      {
        name: 'test_db_2025', // اسم صالح
        shouldFail: false,
        description: 'اسم صالح'
      },
      {
        name: 'MyTestDatabase', // اسم صالح
        shouldFail: false,
        description: 'اسم صالح آخر'
      }
    ];

    for (const testCase of testCases) {
      console.log(`\n📝 اختبار إنشاء قاعدة بيانات: ${testCase.name} (${testCase.description})`);
      
      const createResponse = await fetch('http://localhost:9005/api/database/create', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          connectionId: defaultConnection.id,
          name: testCase.name,
          owner: 'deviceflow_user',
          template: 'template0',
          encoding: 'UTF8'
        })
      });

      console.log(`   الحالة: ${createResponse.status} ${createResponse.statusText}`);
      
      if (testCase.shouldFail) {
        if (!createResponse.ok) {
          const errorData = await createResponse.json();
          console.log(`   ✅ فشل كما متوقع: ${errorData.error}`);
          if (errorData.details) {
            console.log(`   📋 التفاصيل: ${errorData.details}`);
          }
          if (errorData.examples) {
            console.log(`   💡 أمثلة: ${errorData.examples.join(', ')}`);
          }
        } else {
          console.log(`   ❌ كان يجب أن يفشل لكنه نجح!`);
        }
      } else {
        if (createResponse.ok) {
          const result = await createResponse.json();
          console.log(`   ✅ نجح: ${result.message}`);
        } else {
          const errorData = await createResponse.json();
          console.log(`   ❌ فشل: ${errorData.error}`);
        }
      }
    }

    console.log('\n🎉 انتهى اختبار إنشاء قواعد البيانات!');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

testCreateDatabase();
