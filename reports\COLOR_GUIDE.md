# دليل الألوان والتصميم
## Color & Design System Guide

دليل شامل للألوان والتصميم المستخدم في التحسينات.

---

## 🎨 نظام الألوان الأساسي

### الألوان الرئيسية (الوضع النهاري)
```css
/* الأزرق - Primary */
--primary: #3b82f6;
--primary-dark: #1d4ed8;
--primary-light: #93c5fd;
--primary-bg: rgba(59, 130, 246, 0.1);

/* الأخضر - Success */
--success: #10b981;
--success-dark: #059669;
--success-light: #6ee7b7;
--success-bg: rgba(16, 185, 129, 0.1);

/* الأحمر - Danger */
--danger: #ef4444;
--danger-dark: #dc2626;
--danger-light: #fca5a5;
--danger-bg: rgba(239, 68, 68, 0.1);

/* الأصفر - Warning */
--warning: #f59e0b;
--warning-dark: #d97706;
--warning-light: #fcd34d;
--warning-bg: rgba(245, 158, 11, 0.1);

/* البنفسجي - Info */
--info: #8b5cf6;
--info-dark: #7c3aed;
--info-light: #c4b5fd;
--info-bg: rgba(139, 92, 246, 0.1);

/* الرمادي - Neutral */
--gray-50: #f9fafb;
--gray-100: #f3f4f6;
--gray-200: #e5e7eb;
--gray-300: #d1d5db;
--gray-400: #9ca3af;
--gray-500: #6b7280;
--gray-600: #4b5563;
--gray-700: #374151;
--gray-800: #1f2937;
--gray-900: #111827;
```

### 🌙 ألوان الوضع الليلي
```css
/* متغيرات الألوان للوضع الليلي */
.dark-mode {
  --bg-primary: #0f172a;      /* خلفية رئيسية */
  --bg-secondary: #1e293b;    /* خلفية ثانوية */
  --bg-tertiary: #334155;     /* خلفية ثالثية */
  --text-primary: #f8fafc;    /* نص رئيسي */
  --text-secondary: #cbd5e1;  /* نص ثانوي */
  --text-muted: #94a3b8;      /* نص خافت */
  --border-color: #475569;    /* لون الحدود */
  --accent-primary: #3b82f6;  /* لون التمييز الأساسي */
  --accent-success: #10b981;  /* لون النجاح */
  --accent-warning: #f59e0b;  /* لون التحذير */
  --accent-danger: #ef4444;   /* لون الخطر */
}

/* ألوان الشارات في الوضع الليلي */
--badge-blue-dark: #93c5fd;     /* أزرق فاتح */
--badge-green-dark: #6ee7b7;    /* أخضر فاتح */
--badge-red-dark: #fca5a5;      /* أحمر فاتح */
--badge-yellow-dark: #fcd34d;   /* أصفر فاتح */
--badge-purple-dark: #c4b5fd;   /* بنفسجي فاتح */
--badge-gray-dark: #d1d5db;     /* رمادي فاتح */
```

---

## 🌙 استخدام الألوان في الوضع الليلي

### الشارات الملونة في الوضع الليلي
```jsx
{/* شارة زرقاء - الوضع الليلي */}
<div className="enhanced-badge bg-blue-500/10 text-blue-600 border-blue-500/20">
  <Icon className="h-4 w-4 ml-1" />
  النص (سيظهر بلون #93c5fd في الوضع الليلي)
</div>

{/* شارة خضراء - الوضع الليلي */}
<div className="enhanced-badge bg-green-500/10 text-green-600 border-green-500/20">
  <CheckCircle className="h-4 w-4 ml-1" />
  مكتمل (سيظهر بلون #6ee7b7 في الوضع الليلي)
</div>

{/* شارة حمراء - الوضع الليلي */}
<div className="enhanced-badge bg-red-500/10 text-red-600 border-red-500/20">
  <XCircle className="h-4 w-4 ml-1" />
  خطأ (سيظهر بلون #fca5a5 في الوضع الليلي)
</div>

{/* شارة صفراء - الوضع الليلي */}
<div className="enhanced-badge bg-yellow-500/10 text-yellow-600 border-yellow-500/20">
  <AlertTriangle className="h-4 w-4 ml-1" />
  تحذير (سيظهر بلون #fcd34d في الوضع الليلي)
</div>
```

### البطاقات في الوضع الليلي
```jsx
{/* بطاقة أساسية - تتكيف تلقائياً مع الوضع الليلي */}
<Card className="enhanced-card card-primary">
  {/* ستصبح الخلفية rgba(30, 41, 59, 0.9) في الوضع الليلي */}
</Card>

{/* بطاقة نجاح - تتكيف تلقائياً */}
<Card className="enhanced-card card-success">
  {/* ستحافظ على الشريط الأخضر العلوي */}
</Card>
```

### النصوص في الوضع الليلي
```jsx
{/* النصوص تتكيف تلقائياً */}
<h1 className="text-3xl font-bold">
  {/* سيصبح اللون var(--text-primary) = #f8fafc */}
  عنوان رئيسي
</h1>

<p className="text-muted-foreground">
  {/* سيصبح اللون var(--text-secondary) = #cbd5e1 */}
  نص ثانوي
</p>
```

---

## 🏷️ استخدام الألوان في الشارات

### الشارات الملونة
```jsx
{/* شارة زرقاء - للمعلومات العامة */}
<div className="enhanced-badge bg-blue-500/10 text-blue-600 border-blue-500/20">
  <Icon className="h-4 w-4 ml-1" />
  النص
</div>

{/* شارة خضراء - للحالات الإيجابية */}
<div className="enhanced-badge bg-green-500/10 text-green-600 border-green-500/20">
  <CheckCircle className="h-4 w-4 ml-1" />
  مكتمل
</div>

{/* شارة حمراء - للتحذيرات والأخطاء */}
<div className="enhanced-badge bg-red-500/10 text-red-600 border-red-500/20">
  <XCircle className="h-4 w-4 ml-1" />
  خطأ
</div>

{/* شارة صفراء - للتحذيرات */}
<div className="enhanced-badge bg-yellow-500/10 text-yellow-600 border-yellow-500/20">
  <AlertTriangle className="h-4 w-4 ml-1" />
  تحذير
</div>

{/* شارة بنفسجية - للمعلومات الخاصة */}
<div className="enhanced-badge bg-purple-500/10 text-purple-600 border-purple-500/20">
  <Star className="h-4 w-4 ml-1" />
  مميز
</div>

{/* شارة رمادية - للمعلومات المحايدة */}
<div className="enhanced-badge bg-gray-500/10 text-gray-600 border-gray-500/20">
  <Info className="h-4 w-4 ml-1" />
  معلومات
</div>
```

---

## 🃏 ألوان البطاقات

### أنواع البطاقات
```jsx
{/* بطاقة أساسية - زرقاء */}
<Card className="enhanced-card card-primary">
  {/* محتوى البطاقة */}
</Card>

{/* بطاقة نجاح - خضراء */}
<Card className="enhanced-card card-success">
  {/* محتوى البطاقة */}
</Card>

{/* بطاقة تحذير - صفراء */}
<Card className="enhanced-card card-warning">
  {/* محتوى البطاقة */}
</Card>

{/* بطاقة خطر - حمراء */}
<Card className="enhanced-card card-danger">
  {/* محتوى البطاقة */}
</Card>

{/* بطاقة معلومات - بنفسجية */}
<Card className="enhanced-card card-info">
  {/* محتوى البطاقة */}
</Card>
```

---

## 📝 ألوان الأقسام

### أقسام المعلومات
```jsx
{/* قسم المعلومات الأساسية - أزرق */}
<div className="info-section">
  <h3 className="font-semibold text-base flex items-center space-x-2 space-x-reverse mb-3 text-gray-800">
    <Info className="h-4 w-4 text-primary icon-enhanced" />
    <span>معلومات أساسية</span>
  </h3>
  {/* محتوى القسم */}
</div>

{/* قسم النجاح - أخضر */}
<div className="success-section">
  <h3 className="font-semibold text-base flex items-center space-x-2 space-x-reverse mb-3 text-gray-800">
    <CheckCircle className="h-4 w-4 text-green-600 icon-enhanced" />
    <span>العمليات المكتملة</span>
  </h3>
  {/* محتوى القسم */}
</div>

{/* قسم التحذير - أصفر */}
<div className="warning-section">
  <h3 className="font-semibold text-base flex items-center space-x-2 space-x-reverse mb-3 text-gray-800">
    <AlertTriangle className="h-4 w-4 text-yellow-600 icon-enhanced" />
    <span>تحذيرات مهمة</span>
  </h3>
  {/* محتوى القسم */}
</div>
```

---

## 🔘 ألوان الأزرار

### أنواع الأزرار
```jsx
{/* زر أساسي - أزرق */}
<Button className="enhanced-button bg-primary hover:bg-primary/90 text-white">
  <Icon className="ml-2 h-4 w-4" />
  إجراء أساسي
</Button>

{/* زر نجاح - أخضر */}
<Button className="enhanced-button bg-green-600 hover:bg-green-700 text-white">
  <CheckCircle className="ml-2 h-4 w-4" />
  تأكيد
</Button>

{/* زر خطر - أحمر */}
<Button className="enhanced-button variant-destructive bg-red-600 text-white hover:bg-red-700">
  <Trash2 className="ml-2 h-4 w-4" />
  حذف
</Button>

{/* زر ثانوي - أبيض */}
<Button className="enhanced-button variant-outline bg-white text-gray-800 border-gray-300 hover:bg-gray-50">
  <Edit className="ml-2 h-4 w-4" />
  تعديل
</Button>

{/* زر تحذير - أصفر */}
<Button className="enhanced-button bg-yellow-600 hover:bg-yellow-700 text-white">
  <AlertTriangle className="ml-2 h-4 w-4" />
  تحذير
</Button>
```

---

## 📊 ألوان الجداول

### حالات الجدول
```css
/* رأس الجدول */
.enhanced-table thead {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
}

/* صف عند التمرير */
.enhanced-table tr:hover {
  background: rgba(59, 130, 246, 0.05) !important;
}

/* الترقيم */
.row-number {
  color: #6b7280 !important;
}

/* النصوص */
.enhanced-table td {
  color: #374151 !important;
}

.enhanced-table tr:hover td {
  color: #1f2937 !important;
}
```

---

## 🎯 دليل استخدام الألوان

### متى نستخدم كل لون؟

#### الأزرق (Primary)
- **الاستخدام**: المعلومات الأساسية، الإجراءات الرئيسية
- **أمثلة**: أزرار الحفظ، المعلومات العامة، العناوين الرئيسية
- **تجنب**: التحذيرات، الأخطاء

#### الأخضر (Success)
- **الاستخدام**: العمليات المكتملة، الحالات الإيجابية
- **أمثلة**: رسائل النجاح، الأجهزة المطابقة، العمليات المكتملة
- **تجنب**: التحذيرات، المعلومات المحايدة

#### الأحمر (Danger)
- **الاستخدام**: الأخطاء، التحذيرات الخطيرة، الحذف
- **أمثلة**: رسائل الخطأ، أزرار الحذف، الأجهزة المفقودة
- **تجنب**: المعلومات الإيجابية، الإجراءات العادية

#### الأصفر (Warning)
- **الاستخدام**: التحذيرات، الانتباه المطلوب
- **أمثلة**: تحذيرات النظام، الأجهزة الزائدة، المراجعة المطلوبة
- **تجنب**: المعلومات المؤكدة، الحالات الطبيعية

#### البنفسجي (Info)
- **الاستخدام**: المعلومات الخاصة، الميزات المتقدمة
- **أمثلة**: الأجهزة المباعة، الميزات الخاصة، المعلومات الإضافية
- **تجنب**: الإجراءات الأساسية، التحذيرات

#### الرمادي (Neutral)
- **الاستخدام**: المعلومات المحايدة، النصوص العادية
- **أمثلة**: الترقيم، النصوص التوضيحية، المعلومات الثانوية
- **تجنب**: الإجراءات المهمة، التحذيرات

---

## 🌈 تدرجات الألوان

### التدرجات المستخدمة
```css
/* تدرج أزرق */
background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);

/* تدرج أخضر */
background: linear-gradient(135deg, #10b981 0%, #059669 100%);

/* تدرج أحمر */
background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);

/* تدرج أصفر */
background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);

/* تدرج بنفسجي */
background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);

/* تدرج متعدد الألوان */
background: linear-gradient(90deg, #3b82f6, #6366f1, #8b5cf6, #ec4899);
```

---

## 🔧 نصائح للاستخدام

### 1. التباين والوضوح
- استخدم ألوان متباينة للنص والخلفية
- تأكد من وضوح النص على جميع الخلفيات
- اختبر الألوان على شاشات مختلفة

### 2. الاتساق
- استخدم نفس الألوان للمعاني المتشابهة
- حافظ على نظام الألوان في جميع الصفحات
- وثق استخدام كل لون

### 3. إمكانية الوصول
- تأكد من التباين الكافي (4.5:1 على الأقل)
- لا تعتمد على الألوان فقط لنقل المعلومات
- اختبر مع أدوات فحص إمكانية الوصول

---

## 📱 الألوان في التصميم المتجاوب

### تكيف الألوان
```css
/* الشاشات الكبيرة */
@media (min-width: 1024px) {
  .enhanced-badge {
    background: rgba(255, 255, 255, 0.95);
  }
}

/* الشاشات الصغيرة */
@media (max-width: 768px) {
  .enhanced-badge {
    background: rgba(255, 255, 255, 1);
    border-width: 2px;
  }
}
```

---

هذا الدليل يساعد في الحفاظ على اتساق الألوان وجودة التصميم في جميع أجزاء النظام.
