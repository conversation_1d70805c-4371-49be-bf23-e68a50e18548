# أمثلة تطبيقية للتحسينات
## UI Enhancement Examples

هذا الملف يحتوي على أمثلة عملية لتطبيق التحسينات في مختلف أنواع الصفحات والمكونات.

---

## 📄 مثال: صفحة قائمة بسيطة

### قبل التحسين
```jsx
export default function SimpleListPage() {
  return (
    <div className="p-4">
      <h1>قائمة العناصر</h1>
      <div className="mt-4">
        <table className="w-full border">
          <thead>
            <tr>
              <th className="border p-2">الاسم</th>
              <th className="border p-2">النوع</th>
              <th className="border p-2">التاريخ</th>
            </tr>
          </thead>
          <tbody>
            {items.map(item => (
              <tr key={item.id}>
                <td className="border p-2">{item.name}</td>
                <td className="border p-2">{item.type}</td>
                <td className="border p-2">{item.date}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
```

### بعد التحسين
```jsx
import './enhanced-styles.css';
import { Package, Calendar, Tag } from 'lucide-react';

export default function EnhancedListPage() {
  return (
    <div className="page-container">
      {/* Header Section */}
      <div className="header-card p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center space-x-3 space-x-reverse">
              <div className="p-3 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 icon-enhanced">
                <Package className="h-8 w-8 text-primary" />
              </div>
              <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                قائمة العناصر المحسنة
              </span>
            </h1>
            <p className="text-muted-foreground mt-2 text-lg">
              عرض وإدارة جميع العناصر بطريقة احترافية ومنظمة
            </p>
          </div>
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="enhanced-badge bg-blue-500/10 text-blue-600 border-blue-500/20">
              <Package className="h-4 w-4 ml-1 icon-enhanced" />
              إجمالي العناصر: {items.length}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Card className="enhanced-card card-primary mt-6">
        <CardHeader>
          <CardTitle className="text-xl flex items-center space-x-2 space-x-reverse">
            <Package className="h-5 w-5 text-primary icon-enhanced" />
            <span>قائمة العناصر</span>
          </CardTitle>
          <CardDescription>
            جميع العناصر المتاحة في النظام
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="enhanced-scroll-area">
            <table className="enhanced-table">
              <thead>
                <tr>
                  <th className="w-16">#</th>
                  <th>الاسم</th>
                  <th>النوع</th>
                  <th>التاريخ</th>
                  <th className="text-center">إجراءات</th>
                </tr>
              </thead>
              <tbody>
                {items.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="h-32 text-center">
                      <div className="flex flex-col items-center space-y-3 text-muted-foreground">
                        <Package className="h-12 w-12" />
                        <p>لا توجد عناصر لعرضها.</p>
                        <p className="text-sm">ستظهر العناصر هنا عند إضافتها</p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  items.map((item, index) => (
                    <tr key={item.id}>
                      <td className="row-number">{index + 1}</td>
                      <td className="font-medium">{item.name}</td>
                      <td>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Tag className="h-4 w-4 text-gray-500" />
                          <span>{item.type}</span>
                        </div>
                      </td>
                      <td>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Calendar className="h-4 w-4 text-gray-500" />
                          <span>{item.date}</span>
                        </div>
                      </td>
                      <td className="text-center">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="enhanced-button hover:bg-primary/10"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

---

## 📝 مثال: صفحة نموذج

### قبل التحسين
```jsx
export default function SimpleForm() {
  return (
    <div className="p-4">
      <h1>إضافة عنصر جديد</h1>
      <form className="mt-4 space-y-4">
        <div>
          <label>الاسم</label>
          <input type="text" className="w-full border p-2" />
        </div>
        <div>
          <label>النوع</label>
          <select className="w-full border p-2">
            <option>اختر النوع</option>
          </select>
        </div>
        <div>
          <label>الوصف</label>
          <textarea className="w-full border p-2"></textarea>
        </div>
        <button type="submit" className="bg-blue-500 text-white px-4 py-2">
          حفظ
        </button>
      </form>
    </div>
  );
}
```

### بعد التحسين
```jsx
import './enhanced-styles.css';
import { Plus, FileText, Tag, AlignLeft } from 'lucide-react';

export default function EnhancedForm() {
  return (
    <div className="page-container">
      {/* Header */}
      <div className="header-card p-6">
        <h1 className="text-3xl font-bold tracking-tight flex items-center space-x-3 space-x-reverse">
          <div className="p-3 rounded-full bg-gradient-to-br from-success/20 to-success/10 icon-enhanced">
            <Plus className="h-8 w-8 text-success" />
          </div>
          <span className="bg-gradient-to-r from-success to-success/80 bg-clip-text text-transparent">
            إضافة عنصر جديد
          </span>
        </h1>
        <p className="text-muted-foreground mt-2 text-lg">
          أدخل معلومات العنصر الجديد بدقة
        </p>
      </div>

      {/* Form */}
      <Card className="enhanced-card card-success mt-6">
        <CardHeader>
          <CardTitle className="text-xl flex items-center space-x-2 space-x-reverse">
            <FileText className="h-5 w-5 text-success icon-enhanced" />
            <span>معلومات العنصر</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form className="space-y-6">
            {/* Basic Information Section */}
            <div className="info-section">
              <h3 className="font-semibold text-base flex items-center space-x-2 space-x-reverse mb-3 text-gray-800">
                <FileText className="h-4 w-4 text-primary icon-enhanced" />
                <span>المعلومات الأساسية</span>
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="flex items-center space-x-2 space-x-reverse text-sm font-medium text-gray-700">
                    <Tag className="h-4 w-4 text-primary" />
                    <span>اسم العنصر</span>
                  </Label>
                  <Input
                    placeholder="أدخل اسم العنصر"
                    className="enhanced-input bg-white text-gray-800 placeholder-gray-500"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="flex items-center space-x-2 space-x-reverse text-sm font-medium text-gray-700">
                    <Tag className="h-4 w-4 text-primary" />
                    <span>نوع العنصر</span>
                  </Label>
                  <Select>
                    <SelectTrigger className="enhanced-input bg-white text-gray-800">
                      <SelectValue placeholder="اختر النوع" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="type1">النوع الأول</SelectItem>
                      <SelectItem value="type2">النوع الثاني</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Description Section */}
            <div className="warning-section">
              <h3 className="font-semibold text-base flex items-center space-x-2 space-x-reverse mb-3 text-gray-800">
                <AlignLeft className="h-4 w-4 text-warning icon-enhanced" />
                <span>الوصف التفصيلي</span>
              </h3>
              <Textarea
                placeholder="أدخل وصف مفصل للعنصر"
                className="enhanced-input bg-white text-gray-800 placeholder-gray-500 min-h-[100px]"
              />
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-3 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                className="enhanced-button variant-outline bg-white text-gray-800 border-gray-300 hover:bg-gray-50"
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                className="enhanced-button bg-success hover:bg-success/90 text-white"
              >
                <Plus className="ml-2 h-4 w-4" />
                حفظ العنصر
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
```

---

## 🪟 مثال: نافذة منبثقة

```jsx
<Dialog open={isOpen} onOpenChange={setIsOpen}>
  <DialogContent className="enhanced-dialog max-w-3xl">
    <DialogHeader className="enhanced-dialog-header">
      <DialogTitle className="enhanced-dialog-title flex items-center space-x-2 space-x-reverse">
        <Eye className="h-5 w-5 text-primary" />
        <span>تفاصيل العنصر</span>
      </DialogTitle>
      <DialogDescription className="enhanced-dialog-description">
        عرض جميع معلومات العنصر المحدد
      </DialogDescription>
    </DialogHeader>
    <div className="enhanced-scroll-area p-4">
      <Table className="enhanced-modal-table">
        <TableHeader>
          <TableRow>
            <TableHead className="w-16">#</TableHead>
            <TableHead>الخاصية</TableHead>
            <TableHead>القيمة</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {properties.map((prop, index) => (
            <TableRow key={prop.id}>
              <TableCell className="row-number">{index + 1}</TableCell>
              <TableCell className="font-medium">{prop.name}</TableCell>
              <TableCell>{prop.value}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
    <DialogFooter>
      <Button
        variant="outline"
        onClick={() => setIsOpen(false)}
        className="enhanced-modal-button"
      >
        إغلاق
      </Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

---

## 🌙 مثال: تطبيق الوضع الليلي

### إعداد الوضع الليلي في صفحة
```jsx
import './enhanced-styles.css';
import { DarkModeToggle } from './DarkModeToggle';
import { Package, Calendar, Tag } from 'lucide-react';

export default function PageWithDarkMode() {
  return (
    <div className="page-container">
      {/* Header مع زر الوضع الليلي */}
      <div className="header-card p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center space-x-3 space-x-reverse">
              <div className="p-3 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 icon-enhanced">
                <Package className="h-8 w-8 text-primary" />
              </div>
              <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                صفحة مع الوضع الليلي
              </span>
            </h1>
            <p className="text-muted-foreground mt-2 text-lg">
              مثال على تطبيق الوضع الليلي بشكل كامل
            </p>
          </div>
          <div className="flex items-center space-x-3 space-x-reverse">
            {/* زر الوضع الليلي */}
            <DarkModeToggle
              size="md"
              variant="outline"
              className="enhanced-button"
            />

            <div className="enhanced-badge bg-blue-500/10 text-blue-600 border-blue-500/20">
              <Package className="h-4 w-4 ml-1 icon-enhanced" />
              إجمالي العناصر: 150
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Card className="enhanced-card card-primary mt-6">
        <CardHeader>
          <CardTitle className="text-xl flex items-center space-x-2 space-x-reverse">
            <Package className="h-5 w-5 text-primary icon-enhanced" />
            <span>المحتوى الرئيسي</span>
          </CardTitle>
          <CardDescription>
            هذا المحتوى يتكيف تلقائياً مع الوضع الليلي
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="enhanced-scroll-area">
            <table className="enhanced-table">
              <thead>
                <tr>
                  <th className="w-16">#</th>
                  <th>الاسم</th>
                  <th>النوع</th>
                  <th>التاريخ</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="row-number">1</td>
                  <td className="font-medium">عنصر تجريبي</td>
                  <td>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Tag className="h-4 w-4 text-gray-500" />
                      <span>نوع أساسي</span>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span>2024-01-15</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

### أنواع مختلفة من أزرار الوضع الليلي
```jsx
{/* الزر الأساسي */}
<DarkModeToggle
  size="md"
  variant="outline"
  className="enhanced-button"
/>

{/* زر مبسط */}
<DarkModeToggleCompact className="ml-2" />

{/* زر مع نص */}
<DarkModeToggleWithLabel className="flex items-center" />
```

---

## 🎨 نصائح سريعة للتطبيق

### 1. ترتيب الأولويات
```
1. إضافة ملف CSS المحسن
2. تحسين رأس الصفحة
3. تحسين البطاقات الرئيسية
4. تحسين الجداول
5. تحسين النوافذ المنبثقة
```

### 2. الفئات الأساسية المطلوبة
```css
/* الأساسيات */
.page-container
.header-card
.enhanced-card
.enhanced-table
.enhanced-button
.enhanced-input
.enhanced-badge

/* الألوان */
.card-primary
.card-success
.card-warning
.card-danger

/* الأقسام */
.info-section
.success-section
.warning-section
```

### 3. الأيقونات المقترحة
```jsx
// للاستيراد من lucide-react
import {
  Package,      // للعناصر والمنتجات
  FileText,     // للمستندات والنماذج
  Users,        // للمستخدمين
  Settings,     // للإعدادات
  BarChart3,    // للتقارير
  Calendar,     // للتواريخ
  Tag,          // للتصنيفات
  Eye,          // للعرض
  Edit,         // للتعديل
  Trash2,       // للحذف
  Plus,         // للإضافة
  Search,       // للبحث
} from 'lucide-react';
```

---

هذه الأمثلة توضح كيفية تطبيق التحسينات بطريقة عملية ومتدرجة.
