const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// صلاحيات كاملة للمدير
const fullPermissions = {
  dashboard: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  inventory: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  sales: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  supply: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  clients: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  users: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  reports: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  settings: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  maintenance: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  requests: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  stocktaking: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  messaging: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  acceptDevices: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  evaluation: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  maintenanceTransfer: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  delivery: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  returns: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  maintenanceReceipt: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  database: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  auditLogs: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  },
  systemHealth: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
    manage: [1, 2, 3],
    acceptWithoutWarranty: true
  }
};

async function createAdminUser() {
  console.log('🔧 إنشاء مستخدم مدير جديد...');
  
  try {
    // التحقق من وجود مستخدم admin
    const existingAdmin = await prisma.user.findFirst({
      where: {
        OR: [
          { username: 'admin' },
          { role: 'admin' },
          { email: '<EMAIL>' }
        ]
      }
    });

    if (existingAdmin) {
      console.log('✅ يوجد مستخدم admin بالفعل:');
      console.log(`   👤 الاسم: ${existingAdmin.name}`);
      console.log(`   🔑 اسم المستخدم: ${existingAdmin.username}`);
      console.log(`   📧 البريد: ${existingAdmin.email}`);
      console.log(`   🎭 الدور: ${existingAdmin.role}`);
      
      // تحديث الصلاحيات إذا لزم الأمر
      if (!existingAdmin.permissions) {
        console.log('🔄 تحديث صلاحيات المدير...');
        await prisma.user.update({
          where: { id: existingAdmin.id },
          data: {
            permissions: JSON.stringify(fullPermissions),
            role: 'admin'
          }
        });
        console.log('✅ تم تحديث صلاحيات المدير');
      }
      
      return existingAdmin;
    }

    // إنشاء مستخدم admin جديد
    console.log('📝 إنشاء مستخدم admin جديد...');
    
    const newAdmin = await prisma.user.create({
      data: {
        name: 'مدير النظام الرئيسي',
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        status: 'Active',
        phone: '',
        photo: '',
        branchLocation: 'المقر الرئيسي',
        permissions: JSON.stringify(fullPermissions),
        warehouseAccess: JSON.stringify([1, 2, 3]) // وصول لجميع المخازن
      }
    });

    console.log('✅ تم إنشاء مستخدم admin جديد بنجاح:');
    console.log(`   🆔 ID: ${newAdmin.id}`);
    console.log(`   👤 الاسم: ${newAdmin.name}`);
    console.log(`   🔑 اسم المستخدم: ${newAdmin.username}`);
    console.log(`   📧 البريد: ${newAdmin.email}`);
    console.log(`   🎭 الدور: ${newAdmin.role}`);
    console.log(`   🔐 عدد الصلاحيات: ${Object.keys(fullPermissions).length}`);

    return newAdmin;

  } catch (error) {
    console.error('❌ خطأ في إنشاء مستخدم admin:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل الدالة
createAdminUser()
  .then(() => {
    console.log('\n🎉 تم الانتهاء من عملية إنشاء المدير');
    console.log('💡 يمكنك الآن استخدام التطبيق بصلاحيات المدير الكاملة');
  })
  .catch((error) => {
    console.error('💥 فشل في إنشاء المدير:', error);
    process.exit(1);
  });
