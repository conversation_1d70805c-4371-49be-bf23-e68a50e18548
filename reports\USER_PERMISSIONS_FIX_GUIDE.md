# إصلاح مشكلة صلاحيات المستخدمين - اختفاء الأقسام

## 📋 وصف المشكلة

في قسم إدارة المستخدمين، عند تعديل صلاحيات مستخدم، الأقسام المتاحة عند إلغاء قسم على مستخدم كان يختفي ولا يمكن تفعيله مرة أخرى.

## 🔧 سبب المشكلة

المشكلة كانت في منطق `availableSections` في مكون `PermissionsSection`. الكود السابق كان يعرض فقط الأقسام التي لدى المستخدم الحالي (المدير) صلاحية عليها، وعندما يتم إلغاء قسم محدد مسبقًا للمستخدم المراد تعديله، كان هذا القسم يختفي نهائيًا من القائمة.

## ✅ الحل المطبق

### 1. تحديث منطق `availableSections`

```typescript
const availableSections = useMemo(() => {
  if (!currentUser || !currentUser.permissions || typeof currentUser.permissions !== 'object') {
    return allSections;
  }
  
  // الأقسام المتاحة بناءً على صلاحيات المستخدم الحالي
  const userAvailableSections = allSections.filter(section => {
    const userPermission = currentUser.permissions[section.id as keyof AppPermissions];
    return userPermission?.view || userPermission?.create || userPermission?.edit || userPermission?.delete;
  });

  // إضافة الأقسام المحددة حاليًا حتى لو لم تكن في صلاحيات المستخدم الحالي
  const currentlySelectedSectionIds = Object.keys(currentUserPermissions).filter(key => {
    const perm = currentUserPermissions[key as keyof AppPermissions];
    return perm?.view || perm?.create || perm?.edit || perm?.delete;
  });

  const additionalSections = allSections.filter(section => 
    currentlySelectedSectionIds.includes(section.id) && 
    !userAvailableSections.some(available => available.id === section.id)
  );

  return [...userAvailableSections, ...additionalSections];
}, [currentUser, currentUserPermissions]);
```

### 2. تحديث منطق `handleSectionToggle`

```typescript
const handleSectionToggle = (sectionId: string) => {
  const isCurrentlySelected = selectedSections.includes(sectionId);
  const hasCurrentUserAccess = currentUser?.permissions?.[sectionId as keyof AppPermissions];
  const canManageSection = hasCurrentUserAccess?.view || hasCurrentUserAccess?.create || 
                         hasCurrentUserAccess?.edit || hasCurrentUserAccess?.delete;
  
  // يمكن إلغاء أي قسم محدد، لكن لا يمكن إضافة قسم إلا إذا كان للمستخدم الحالي صلاحية عليه
  if (!isCurrentlySelected && !canManageSection) return;
  
  // باقي الكود...
};
```

### 3. تحسين واجهة المستخدم

- إضافة تمييز بصري للأقسام المحددة مسبقًا
- إضافة تلميحات توضيحية
- تعطيل الأزرار للأقسام غير المتاحة
- إضافة علامات توضيحية ("محدد مسبقًا" / "غير متاح")

## 🎯 النتيجة

### ما تم إصلاحه:
✅ **الأقسام المحددة مسبقًا تبقى ظاهرة**: الأقسام التي تم تحديدها مسبقًا للمستخدم تبقى في القائمة حتى لو لم يكن للمدير الحالي صلاحية عليها

✅ **يمكن إلغاء الأقسام المحددة مسبقًا**: يمكن للمدير إلغاء أي قسم محدد للمستخدم

✅ **واجهة مستخدم واضحة**: تمييز بصري واضح بين الأقسام المختلفة

### القيود المفروضة:
❌ **لا يمكن إعادة إضافة أقسام غير مسموحة**: إذا ألغى المدير قسمًا لا يملك صلاحية عليه، لا يمكن إعادة إضافته

✅ **أمان الصلاحيات محافظ عليه**: لا يمكن للمدير منح صلاحيات على أقسام لا يملك صلاحية عليها

## 📂 الملفات المتأثرة

- `components/permissions-section.tsx` - المكون الرئيسي للصلاحيات
- `test-permissions-fix.js` - ملف اختبار الإصلاح

## 🧪 كيفية الاختبار

1. قم بتشغيل الاختبار:
```bash
node test-permissions-fix.js
```

2. اختبر في الواجهة:
   - انتقل إلى صفحة إدارة المستخدمين
   - اختر مستخدم للتعديل
   - انتقل إلى تبويب الصلاحيات
   - ألغِ قسمًا محددًا مسبقًا
   - لاحظ أن القسم يبقى في القائمة ولكن معطل
   - أضف قسمًا جديدًا (إذا كان لديك صلاحية عليه)

## 📋 ملاحظات إضافية

- هذا الإصلاح يحافظ على أمان النظام
- لا يتيح للمديرين منح صلاحيات على أقسام لا يملكون صلاحية عليها
- يحسن تجربة المستخدم بإبقاء الأقسام المحددة مسبقًا ظاهرة
- يوضح للمستخدم حالة كل قسم بصريًا

## 🔄 إصدارات سابقة

**قبل الإصلاح**: الأقسام المحددة مسبقًا تختفي عند إلغائها ولا يمكن رؤيتها أو إعادة تفعيلها

**بعد الإصلاح**: الأقسام المحددة مسبقًا تبقى ظاهرة مع إمكانية إلغائها، ولكن لا يمكن إعادة تفعيلها إلا إذا كان للمدير صلاحية عليها
