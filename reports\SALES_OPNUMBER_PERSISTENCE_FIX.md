# إصلاح الاحتفاظ برقم الأمر في صفحة المبيعات
## Sales Order Number Persistence Fix

---

## المشكلة الأصلية

في صفحة المبيعات (`app/(main)/sales/page.tsx`)، كان رقم الأمر (opNumber) يتم مسحه عند إنشاء فاتورة جديدة أو بعد حفظ فاتورة، مما يجبر المستخدم على إعادة إدخال نفس الرقم في كل مرة.

### المشكلة:
- عند الضغط على "فاتورة بيع جديدة"، يتم مسح رقم الأمر
- بعد حفظ فاتورة، يتم مسح رقم الأمر
- المستخدم يريد الاحتفاظ برقم الأمر نفسه لعدة فواتير متتالية

---

## الحل المطبق

### ✅ 1. تعديل دالة `resetPage`
**إضافة معامل للاحتفاظ برقم الأمر**:
```typescript
// الكود القديم
const resetPage = () => {
  setFormState(initialFormState);
  // ... باقي الكود
};

// الكود الجديد
const resetPage = (preserveOpNumber = false) => {
  // ✅ الحفاظ على opNumber إذا طُلب ذلك
  const currentOpNumber = preserveOpNumber ? formState.opNumber : '';
  
  setFormState({
    ...initialFormState,
    opNumber: currentOpNumber, // ✅ الحفاظ على رقم الأمر إذا كان موجوداً
  });
  // ... باقي الكود
};
```

### ✅ 2. تحديث دالة `proceedWithNewSale`
**الاحتفاظ برقم الأمر عند إنشاء فاتورة جديدة**:
```typescript
// الكود القديم
const proceedWithNewSale = () => {
  resetPage();
  setIsCreateMode(true);
  // ...
};

// الكود الجديد
const proceedWithNewSale = () => {
  // ✅ الحفاظ على opNumber عند إنشاء فاتورة جديدة
  const hasOpNumber = formState.opNumber && formState.opNumber.trim() !== '';
  resetPage(hasOpNumber); // ✅ تمرير معامل للحفاظ على opNumber
  setIsCreateMode(true);
  // ...
};
```

### ✅ 3. تحديث دالة `handleSaveSale`
**الاحتفاظ برقم الأمر بعد حفظ الفاتورة**:
```typescript
// الكود القديم
// مسح المسودة بعد الحفظ الناجح
localStorage.removeItem('saleOrderDraft');
setIsDraft(false);
setHasSavedDraft(false);
setIsCreateMode(false);
resetPage();

// الكود الجديد
// مسح المسودة بعد الحفظ الناجح
localStorage.removeItem('saleOrderDraft');
setIsDraft(false);
setHasSavedDraft(false);
setIsCreateMode(false);

// ✅ الاحتفاظ برقم الأمر إذا كان موجوداً
const shouldPreserveOpNumber = formState.opNumber && formState.opNumber.trim() !== '';
resetPage(shouldPreserveOpNumber);
```

### ✅ 4. تحديث دالة `confirmUpdateSale`
**الاحتفاظ برقم الأمر بعد تحديث الفاتورة**:
```typescript
// الكود القديم
setIsUpdateConfirmOpen(false);
resetPage();

// الكود الجديد
setIsUpdateConfirmOpen(false);

// ✅ الاحتفاظ برقم الأمر إذا كان موجوداً
const shouldPreserveOpNumber = formState.opNumber && formState.opNumber.trim() !== '';
resetPage(shouldPreserveOpNumber);
```

### ✅ 5. تحسين واجهة المستخدم
**إضافة مؤشر بصري وزر مسح**:
```jsx
<div className="space-y-2">
  <div className="flex gap-2">
    <Input
      id="orderId"
      className="enhanced-input h-10 text-sm flex-1"
      placeholder={isCreateMode || loadedSale ? "أدخل رقم الفاتورة الرسمية" : "اضغط 'فاتورة بيع جديدة' للبدء"}
      value={formState.opNumber || ''}
      onChange={(e) =>
        setFormState({ ...formState, opNumber: e.target.value || '' })
      }
      disabled={!isCreateMode && !loadedSale}
    />
    {/* ✅ زر مسح رقم الأمر */}
    {formState.opNumber && (isCreateMode || loadedSale) && (
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={() => setFormState({ ...formState, opNumber: '' })}
        className="h-10 px-3 text-xs"
        title="مسح رقم الأمر"
      >
        ✕
      </Button>
    )}
  </div>
  {/* ✅ مؤشر بصري للاحتفاظ برقم الأمر */}
  {formState.opNumber && (
    <div className="flex items-center space-x-2 text-xs text-green-600 dark:text-green-400">
      <span>✅ سيتم الاحتفاظ برقم الأمر عند إنشاء فاتورة جديدة</span>
    </div>
  )}
</div>
```

---

## النتيجة النهائية

### ✅ المشاكل المحلولة:
1. **الاحتفاظ برقم الأمر** عند إنشاء فاتورة جديدة
2. **الاحتفاظ برقم الأمر** بعد حفظ فاتورة
3. **الاحتفاظ برقم الأمر** بعد تحديث فاتورة موجودة
4. **مؤشر بصري** يوضح للمستخدم أن الرقم سيتم الاحتفاظ به
5. **زر مسح** للتحكم في إزالة رقم الأمر عند الحاجة

### 🎯 السلوك الجديد:
1. **عند إدخال رقم أمر**: يظهر مؤشر أخضر يوضح أنه سيتم الاحتفاظ بالرقم
2. **عند إنشاء فاتورة جديدة**: يتم الاحتفاظ برقم الأمر إذا كان موجوداً
3. **بعد حفظ فاتورة**: يتم الاحتفاظ برقم الأمر للفاتورة التالية
4. **زر المسح**: يسمح للمستخدم بإزالة رقم الأمر عند الحاجة
5. **المسودات**: تحتفظ برقم الأمر كما هو مطلوب

### 📋 حالات الاستخدام:
- **فواتير متتالية بنفس رقم الأمر**: المستخدم يدخل الرقم مرة واحدة ويستمر في إنشاء فواتير
- **تغيير رقم الأمر**: المستخدم يمكنه تعديل الرقم أو مسحه باستخدام الزر
- **فواتير بدون رقم أمر**: إذا لم يدخل المستخدم رقماً، يعمل النظام كما هو معتاد

### 🔄 التوافق مع الوظائف الموجودة:
- **المسودات**: تعمل بنفس الطريقة وتحتفظ برقم الأمر
- **تحديث الفواتير**: يحتفظ برقم الأمر بعد التحديث
- **حفظ الفواتير**: يستخدم `opNumber` أو `soNumber` كما هو مطلوب
- **التحقق من البيانات**: لا يتأثر بالتغييرات

---

*تم إصلاح مشكلة الاحتفاظ برقم الأمر بنجاح مع الحفاظ على جميع الوظائف الموجودة*
