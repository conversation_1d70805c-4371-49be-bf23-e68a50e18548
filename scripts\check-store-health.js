const fs = require('fs');

console.log('🔍 فحص ملف store.tsx...');

try {
  const content = fs.readFileSync('context/store.tsx', 'utf8');
  
  // التحقق من exports
  const hasStoreProvider = content.includes('export function StoreProvider');
  const hasUseStore = content.includes('export function useStore');
  
  console.log('📊 نتائج الفحص:');
  console.log('✅ StoreProvider export:', hasStoreProvider ? '✓ موجود' : '❌ مفقود');
  console.log('✅ useStore export:', hasUseStore ? '✓ موجود' : '❌ مفقود');
  
  // فحص بنية أساسية
  const hasValueObject = content.includes('const value = {');
  const hasReturn = content.includes('return (');
  const hasProvider = content.includes('<StoreContext.Provider');
  
  console.log('🔧 فحص البنية:');
  console.log('✅ value object:', hasValueObject ? '✓ موجود' : '❌ مفقود');
  console.log('✅ return statement:', hasReturn ? '✓ موجود' : '❌ مفقود');
  console.log('✅ Provider component:', hasProvider ? '✓ موجود' : '❌ مفقود');
  
  // فحص التوازن في الأقواس
  const openBraces = (content.match(/\{/g) || []).length;
  const closeBraces = (content.match(/\}/g) || []).length;
  
  console.log('🔍 فحص الأقواس:');
  console.log(`   - أقواس فتح: ${openBraces}`);
  console.log(`   - أقواس إغلاق: ${closeBraces}`);
  console.log(`   - متوازنة: ${openBraces === closeBraces ? '✓ نعم' : '❌ لا'}`);
  
  // فحص imports للـ api-client
  const hasApiClientImport = content.includes('@/lib/api-client');
  console.log('🔗 API Client import:', hasApiClientImport ? '✓ موجود' : '❌ مفقود');
  
  if (hasStoreProvider && hasUseStore && hasValueObject && hasReturn && hasProvider && openBraces === closeBraces) {
    console.log('\n🎉 الملف يبدو صحيحاً! يجب أن يعمل التطبيق الآن.');
  } else {
    console.log('\n⚠️  هناك مشاكل في الملف تحتاج لإصلاح.');
  }
  
} catch (error) {
  console.error('❌ خطأ في قراءة الملف:', error.message);
}
