
import { MaintenanceOrder, MaintenanceReceiptOrder, DeliveryOrder } from '../lib/types';

// تنفيذ الوظائف المحدثة التي تستخدم API

export async function addMaintenanceOrderAPI(order: MaintenanceOrder) {
  try {
    // إذا لم يكن هناك رقم أمر، نقوم بإنشاء واحد
    if (!order.orderNumber) {
      order.orderNumber = `MAINT-${order.id || Date.now()}`;
    }
    
    // إرسال الأمر إلى API
    const response = await fetch('/api/maintenance-orders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...order,
        status: order.status || 'wip',
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create maintenance order');
    }

    // استقبال الأمر الذي تم إنشاؤه من API
    const newOrder = await response.json();
    return newOrder;
  } catch (error) {
    console.error('Failed to add maintenance order:', error);
    throw error;
  }
}

export async function updateMaintenanceOrderAPI(updatedOrder: MaintenanceOrder) {
  try {
    // إرسال الأمر إلى API
    const response = await fetch('/api/maintenance-orders', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updatedOrder),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update maintenance order');
    }

    // استقبال الأمر المحدّث من API
    const savedOrder = await response.json();
    return savedOrder;
  } catch (error) {
    console.error('Failed to update maintenance order:', error);
    throw error;
  }
}

export async function deleteMaintenanceOrderAPI(orderId: number) {
  try {
    // إرسال طلب الحذف إلى API
    const response = await fetch('/api/maintenance-orders', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id: orderId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete maintenance order');
    }

    return true;
  } catch (error) {
    console.error('Failed to delete maintenance order:', error);
    throw error;
  }
}

export async function addMaintenanceReceiptOrderAPI(order: MaintenanceReceiptOrder) {
  try {
    // إذا لم يكن هناك رقم للاستلام، نقوم بإنشاء واحد
    if (!order.receiptNumber) {
      order.receiptNumber = `MREC-${order.id || Date.now()}`;
    }
    
    // إرسال الأمر إلى API
    const response = await fetch('/api/maintenance-receipts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create maintenance receipt order');
    }

    // استقبال الأمر الذي تم إنشاؤه من API
    const newOrder = await response.json();
    return newOrder;
  } catch (error) {
    console.error('Failed to add maintenance receipt order:', error);
    throw error;
  }
}

export async function updateMaintenanceReceiptOrderAPI(updatedOrder: MaintenanceReceiptOrder) {
  try {
    // إرسال الأمر إلى API
    const response = await fetch('/api/maintenance-receipts', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updatedOrder),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update maintenance receipt order');
    }

    // استقبال الأمر المحدّث من API
    const savedOrder = await response.json();
    return savedOrder;
  } catch (error) {
    console.error('Failed to update maintenance receipt order:', error);
    throw error;
  }
}

export async function deleteMaintenanceReceiptOrderAPI(orderId: number) {
  try {
    // إرسال طلب الحذف إلى API
    const response = await fetch('/api/maintenance-receipts', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id: orderId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete maintenance receipt order');
    }

    return true;
  } catch (error) {
    console.error('Failed to delete maintenance receipt order:', error);
    throw error;
  }
}

export async function addDeliveryOrderAPI(order: DeliveryOrder) {
  try {
    // إذا لم يكن هناك رقم للتسليم، نقوم بإنشاء واحد
    if (!order.deliveryNumber) {
      order.deliveryNumber = `DEL-${order.id || Date.now()}`;
    }
    
    // إرسال الأمر إلى API
    const response = await fetch('/api/delivery-orders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create delivery order');
    }

    // استقبال الأمر الذي تم إنشاؤه من API
    const newOrder = await response.json();
    return newOrder;
  } catch (error) {
    console.error('Failed to add delivery order:', error);
    throw error;
  }
}

export async function updateDeliveryOrderAPI(updatedOrder: DeliveryOrder) {
  try {
    // إرسال الأمر إلى API
    const response = await fetch('/api/delivery-orders', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updatedOrder),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update delivery order');
    }

    // استقبال الأمر المحدّث من API
    const savedOrder = await response.json();
    return savedOrder;
  } catch (error) {
    console.error('Failed to update delivery order:', error);
    throw error;
  }
}

export async function deleteDeliveryOrderAPI(orderId: number) {
  try {
    // إرسال طلب الحذف إلى API
    const response = await fetch('/api/delivery-orders', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id: orderId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete delivery order');
    }

    return true;
  } catch (error) {
    console.error('Failed to delete delivery order:', error);
    throw error;
  }
}
