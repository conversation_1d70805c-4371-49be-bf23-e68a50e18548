async function testUploadFix() {
  try {
    console.log('اختبار رفع المرفقات...');

    // إنشاء ملف تجريبي
    const testFileContent = new Blob(['هذا ملف تجريبي للاختبار'], { type: 'text/plain' });
    const testFile = new File([testFileContent], 'test-file.txt', { type: 'text/plain' });

    // إنشاء FormData
    const formData = new FormData();
    formData.append('files', testFile);
    formData.append('section', 'supply');

    console.log('محاولة رفع الملف إلى قسم supply...');

    // محاولة رفع الملف
    const response = await fetch('http://localhost:9005/api/upload', {
      method: 'POST',
      body: formData,
    });

    const result = await response.json();

    if (response.ok && result.success) {
      console.log('✅ تم رفع الملف بنجاح!');
      console.log('✅ الرسالة:', result.message);
      console.log('✅ الملفات المرفوعة:', result.files.length);
      
      // عرض تفاصيل الملفات
      result.files.forEach((file, index) => {
        console.log(`  الملف ${index + 1}:`);
        console.log(`    الاسم الأصلي: ${file.originalName}`);
        console.log(`    اسم الملف الجديد: ${file.fileName}`);
        console.log(`    المسار: ${file.filePath}`);
        console.log(`    الحجم: ${file.size} بايت`);
        console.log(`    النوع: ${file.type}`);
      });

      console.log('\n🎉 تم حل مشكلة رفع المرفقات بنجاح!');
      
    } else {
      console.log('❌ فشل في رفع الملف');
      console.log('❌ حالة الاستجابة:', response.status);
      console.log('❌ رسالة الخطأ:', result.error || result.message);
      
      if (result.error === 'قسم غير صالح') {
        console.log('🚨 المشكلة: قسم "supply" غير مدرج في القائمة المسموحة!');
      }
    }

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

// تشغيل الاختبار
testUploadFix();
