// اختبار تشخيص مشكلة اختفاء الأقسام 
// فحص بيانات المستخدم الحالي وصلاحياته

console.log('🔍 تشخيص مشكلة اختفاء الأقسام');
console.log('================================');

// محاكاة مشكلة: مستخدم لديه صلاحية على المخزون فقط
const problematicUser = {
  id: 1,
  name: 'مدير النظام',
  username: 'admin',
  email: '<EMAIL>',
  permissions: {
    dashboard: { view: false, create: false, edit: false, delete: false },
    clients: { view: false, create: false, edit: false, delete: false },
    warehouses: { view: false, create: false, edit: false, delete: false },
    inventory: { view: true, create: true, edit: true, delete: true }, // المخزون فقط
    supply: { view: false, create: false, edit: false, delete: false },
    sales: { view: false, create: false, edit: false, delete: false },
    reports: { view: false, create: false, edit: false, delete: false }
  }
};

// مستخدم مدير صحيح مع جميع الصلاحيات
const correctAdminUser = {
  id: 1,
  name: 'مدير النظام',
  username: 'admin',
  email: '<EMAIL>',
  permissions: {
    dashboard: { view: true, create: true, edit: true, delete: true },
    clients: { view: true, create: true, edit: true, delete: true },
    warehouses: { view: true, create: true, edit: true, delete: true },
    inventory: { view: true, create: true, edit: true, delete: true },
    supply: { view: true, create: true, edit: true, delete: true },
    sales: { view: true, create: true, edit: true, delete: true },
    reports: { view: true, create: true, edit: true, delete: true }
  }
};

const allSections = [
  { id: 'dashboard', label: 'الرئيسية', icon: '📊' },
  { id: 'clients', label: 'العملاء', icon: '👥' },
  { id: 'warehouses', label: 'إدارة المخازن', icon: '🏬' },
  { id: 'inventory', label: 'المخزون', icon: '📦' },
  { id: 'supply', label: 'التوريد', icon: '🚚' },
  { id: 'sales', label: 'المبيعات', icon: '💰' },
  { id: 'reports', label: 'التقارير', icon: '📈' }
];

function getAvailableSections(allSections, currentUser) {
  console.log('🔍 تشخيص availableSections:');
  console.log('currentUser:', currentUser.name, currentUser.username);
  
  if (!currentUser || !currentUser.permissions || typeof currentUser.permissions !== 'object') {
    console.log('⚠️ لا توجد صلاحيات للمستخدم الحالي - عرض جميع الأقسام');
    return allSections;
  }
  
  // إذا كان المستخدم مدير (admin)، اعرض جميع الأقسام
  if (currentUser.username === 'admin' || currentUser.id === 1) {
    console.log('👑 المستخدم مدير - عرض جميع الأقسام');
    return allSections;
  }
  
  // جميع الأقسام التي يملك المستخدم الحالي صلاحية عليها
  const userAvailableSections = allSections.filter(section => {
    const userPermission = currentUser.permissions[section.id];
    const hasAccess = userPermission?.view || userPermission?.create || userPermission?.edit || userPermission?.delete;
    console.log(`${section.label} (${section.id}):`, userPermission, '-> hasAccess:', hasAccess);
    return hasAccess;
  });

  console.log('✅ الأقسام المتاحة:', userAvailableSections.map(s => s.label));
  return userAvailableSections;
}

// اختبار الحالة المشكلة
console.log('🚨 اختبار الحالة المشكلة (مدير بصلاحيات محدودة):');
const problematicSections = getAvailableSections(allSections, problematicUser);
console.log('النتيجة:', problematicSections.map(s => s.label));

console.log('\n✅ اختبار الحالة الصحيحة (مدير بجميع الصلاحيات):');
const correctSections = getAvailableSections(allSections, correctAdminUser);
console.log('النتيجة:', correctSections.map(s => s.label));

console.log('\n📋 التشخيص:');
if (problematicSections.length === 1 && problematicSections[0].id === 'inventory') {
  console.log('❌ المشكلة مؤكدة: المستخدم المدير لديه صلاحية على المخزون فقط');
  console.log('🔧 الحل: إما إصلاح صلاحيات المدير، أو إضافة حماية خاصة للمدير');
}

if (correctSections.length === allSections.length) {
  console.log('✅ الحل يعمل: المدير مع الصلاحيات الصحيحة يرى جميع الأقسام');
}

console.log('\n🛠️ الحلول المقترحة:');
console.log('1. التحقق من أن المدير (admin) لديه جميع الصلاحيات');
console.log('2. إضافة حماية خاصة في الكود للمدير');
console.log('3. إصلاح بيانات المدير في قاعدة البيانات');

console.log('\n✅ التشخيص مكتمل!');
