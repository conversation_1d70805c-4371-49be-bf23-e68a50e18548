const { default: fetch } = require('node-fetch');

async function testAPI() {
  const token = 'dXNlcjphZG1pbjphZG1pbg=='; // user:admin:admin
  
  console.log('🧪 اختبار API endpoints...');
  
  // Test settings API
  try {
    const response = await fetch('http://localhost:9005/api/settings', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Settings API يعمل:', Object.keys(data));
    } else {
      console.log('❌ Settings API خطأ:', response.status, await response.text());
    }
  } catch (error) {
    console.error('❌ خطأ في Settings API:', error.message);
  }
  
  // Test database connections API
  try {
    const response = await fetch('http://localhost:9005/api/database/connections', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Database Connections API يعمل:', data.length, 'connections');
    } else {
      console.log('❌ Database Connections API خطأ:', response.status, await response.text());
    }
  } catch (error) {
    console.error('❌ خطأ في Database Connections API:', error.message);
  }
  
  // Test warehouse creation
  try {
    const response = await fetch('http://localhost:9005/api/warehouses', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: 'Test Warehouse ' + Date.now(),
        type: 'فرعي',
        location: 'Test Location'
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Warehouse Creation API يعمل:', data.name);
    } else {
      console.log('❌ Warehouse Creation API خطأ:', response.status, await response.text());
    }
  } catch (error) {
    console.error('❌ خطأ في Warehouse Creation API:', error.message);
  }
}

testAPI();
