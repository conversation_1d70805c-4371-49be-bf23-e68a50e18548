const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkConnection() {
  try {
    const conn = await prisma.databaseConnection.findFirst();
    console.log('Connection details:');
    console.log('Name:', conn?.name);
    console.log('Host:', conn?.host);
    console.log('Database:', conn?.database);
    console.log('Username:', conn?.username);
    console.log('Password:', conn?.password);
    console.log('Is hashed?', conn?.password?.startsWith('$2'));
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkConnection();
