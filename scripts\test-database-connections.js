// اختبار إضافة اتصال قاعدة بيانات
const token = Buffer.from('admin:admin').toString('base64');

async function testDatabaseConnections() {
  console.log('🧪 اختبار API لاتصالات قواعد البيانات...\n');

  try {
    // 1. اختبار جلب الاتصالات
    console.log('1. اختبار جلب الاتصالات...');
    const getResponse = await fetch('http://localhost:9005/api/database/connections', {
      method: 'GET',
      headers: {
        'Authorization': `Basic ${token}`,
        'Content-Type': 'application/json'
      }
    });
    console.log(`   GET /api/database/connections: ${getResponse.status} ${getResponse.statusText}`);
    if (getResponse.ok) {
      const data = await getResponse.json();
      console.log(`   البيانات: ${JSON.stringify(data, null, 2)}`);
    }

    // 2. اختبار إضافة اتصال جديد
    console.log('\n2. اختبار إضافة اتصال جديد...');
    const newConnection = {
      name: 'اختبار اتصال',
      host: 'localhost',
      port: 5432,
      database: 'test_db',
      username: 'test_user',
      password: 'test_pass',
      isDefault: false
    };

    const postResponse = await fetch('http://localhost:9005/api/database/connections', {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newConnection)
    });
    console.log(`   POST /api/database/connections: ${postResponse.status} ${postResponse.statusText}`);
    if (postResponse.ok) {
      const result = await postResponse.json();
      console.log(`   النتيجة: ${JSON.stringify(result, null, 2)}`);
    } else {
      const errorText = await postResponse.text();
      console.log(`   خطأ: ${errorText}`);
    }

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

testDatabaseConnections();
