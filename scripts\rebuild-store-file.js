const fs = require('fs');
const path = require('path');

console.log('🔧 إنشاء نسخة جديدة من store.tsx...');

try {
  // قراءة الملف
  const filePath = path.join(process.cwd(), 'context', 'store.tsx');
  const content = fs.readFileSync(filePath, 'utf8');
  
  // تجزئة الملف إلى أجزاء مهمة
  const clientDirective = content.match(/"use client";/);
  const imports = content.match(/import[\s\S]+?from.+?["'];/g);
  
  // البحث عن تعريف StoreContext
  const storeContextMatch = content.match(/const StoreContext = createContext[\s\S]+?;/);
  
  // البحث عن StoreProvider
  let storeProviderMatch = content.match(/export function StoreProvider[\s\S]+?return \(\s*<StoreContext\.Provider[\s\S]+?<\/StoreContext\.Provider>\s*\);/);
  if (!storeProviderMatch) {
    console.error('❌ لم يتم العثور على تعريف كامل لـ StoreProvider');
    return;
  }
  
  // البحث عن useStore
  let useStoreMatch = content.match(/export function useStore\(\)[\s\S]+?return context;/);
  if (!useStoreMatch) {
    console.error('❌ لم يتم العثور على تعريف كامل لـ useStore');
    return;
  }
  
  // إعادة بناء الملف
  let newContent = '';
  
  // إضافة directive
  if (clientDirective) {
    newContent += clientDirective[0] + '\n\n';
  } else {
    newContent += '"use client";\n\n';
  }
  
  // إضافة الاستيرادات
  if (imports && imports.length > 0) {
    newContent += imports.join('\n') + '\n\n';
  }
  
  // إضافة تعريف السياق
  if (storeContextMatch) {
    newContent += storeContextMatch[0] + '\n\n';
  }
  
  // إضافة StoreProvider
  newContent += storeProviderMatch[0] + '\n}\n\n';
  
  // إضافة useStore
  newContent += useStoreMatch[0] + '\n}';
  
  // كتابة الملف المحدث
  fs.writeFileSync(filePath, newContent, 'utf8');
  
  // التحقق من توازن الأقواس
  const openBraces = (newContent.match(/\{/g) || []).length;
  const closeBraces = (newContent.match(/\}/g) || []).length;
  
  console.log(`- أقواس فتح: ${openBraces}`);
  console.log(`- أقواس إغلاق: ${closeBraces}`);
  
  if (openBraces === closeBraces) {
    console.log('✅ الأقواس متوازنة!');
  } else {
    console.log(`❌ الأقواس غير متوازنة! الفرق: ${openBraces - closeBraces}`);
  }
  
  console.log('✅ تم إعادة بناء الملف بنجاح!');
  
} catch (error) {
  console.error('❌ حدث خطأ:', error.message);
}
