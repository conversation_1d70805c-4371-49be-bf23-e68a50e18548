// Test script for supply order deletion
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testSupplyOrderDeletion() {
  console.log('🗑️ اختبار حذف أوامر التوريد...');
  
  try {
    // 1. Create a test supply order first
    console.log('\n1️⃣ إنشاء أمر توريد للاختبار...');
    const testOrder = await prisma.supplyOrder.create({
      data: {
        supplyOrderId: `DELETE-TEST-${Date.now()}`,
        supplierId: 1,
        invoiceNumber: `INV-${Date.now()}`,
        supplyDate: new Date().toISOString(),
        warehouseId: 1,
        employeeName: 'Test Admin',
        items: JSON.stringify([
          {
            imei: `DELETE-TEST-${Date.now()}`,
            manufacturer: 'Test',
            model: 'Test Model',
            condition: 'جديد'
          }
        ]),
        notes: 'أمر اختبار للحذف',
        status: 'completed'
      }
    });
    
    console.log(`✅ تم إنشاء أمر التوريد: ${testOrder.supplyOrderId} (ID: ${testOrder.id})`);
    
    // 2. Test the deletion API
    console.log('\n2️⃣ اختبار API الحذف...');
    
    const deleteResponse = await fetch('http://localhost:9005/api/supply', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + btoa('user:admin:admin')
      },
      body: JSON.stringify({ id: testOrder.id })
    });
    
    if (deleteResponse.ok) {
      console.log('✅ تم حذف أمر التوريد بنجاح من API');
      
      // 3. Verify deletion
      const deletedOrder = await prisma.supplyOrder.findUnique({
        where: { id: testOrder.id }
      });
      
      if (!deletedOrder) {
        console.log('✅ تم التأكد من حذف أمر التوريد من قاعدة البيانات');
      } else {
        console.log('❌ أمر التوريد ما زال موجوداً في قاعدة البيانات');
      }
    } else {
      const errorText = await deleteResponse.text();
      console.log('❌ فشل حذف أمر التوريد:', deleteResponse.status);
      console.log('تفاصيل الخطأ:', errorText);
    }
    
    // 4. List remaining orders
    const remainingOrders = await prisma.supplyOrder.findMany({
      orderBy: { id: 'desc' },
      take: 5
    });
    
    console.log(`\n📋 آخر ${remainingOrders.length} أوامر توريد:`)
    remainingOrders.forEach((order, index) => {
      console.log(`${index + 1}. ${order.supplyOrderId} - ${order.employeeName}`);
    });
    
  } catch (error) {
    console.error('❌ خطأ في اختبار الحذف:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testSupplyOrderDeletion();
