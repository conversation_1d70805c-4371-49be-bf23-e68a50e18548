'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from '@/components/ui/tabs';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Users, Building, Plus, Search } from 'lucide-react';

interface Contact {
  id: number;
  name: string;
  phone: string;
  email: string;
  type: 'client' | 'supplier';
}

export default function ClientsPage() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'clients' | 'suppliers'>('clients');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingContact, setEditingContact] = useState<Contact | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: ''
  });

  // تحميل البيانات الأولية
  useEffect(() => {
    const loadContacts = async () => {
      try {
        // بيانات تجريبية
        const mockContacts: Contact[] = [
          {
            id: 1,
            name: 'شركة الأحمد للتجارة',
            phone: '0501234567',
            email: '<EMAIL>',
            type: 'client'
          },
          {
            id: 2,
            name: 'مؤسسة السلام',
            phone: '0502345678',
            email: '<EMAIL>',
            type: 'client'
          },
          {
            id: 3,
            name: 'مورد المواد الأولية',
            phone: '0503456789',
            email: '<EMAIL>',
            type: 'supplier'
          },
          {
            id: 4,
            name: 'شركة التوريدات المتقدمة',
            phone: '0504567890',
            email: '<EMAIL>',
            type: 'supplier'
          }
        ];

        // محاكاة وقت التحميل
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setContacts(mockContacts);
      } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadContacts();
  }, []);

  // تصفية العملاء
  const filteredClients = contacts.filter(contact => 
    contact.type === 'client' && (
      contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.phone.includes(searchTerm) ||
      contact.email.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  // تصفية الموردين
  const filteredSuppliers = contacts.filter(contact => 
    contact.type === 'supplier' && (
      contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.phone.includes(searchTerm) ||
      contact.email.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  // فتح نافذة الإضافة/التحرير
  const openDialog = (contact?: Contact) => {
    if (contact) {
      setEditingContact(contact);
      setFormData({
        name: contact.name,
        phone: contact.phone,
        email: contact.email
      });
    } else {
      setEditingContact(null);
      setFormData({ name: '', phone: '', email: '' });
    }
    setIsDialogOpen(true);
  };

  // إغلاق النافذة
  const closeDialog = () => {
    setIsDialogOpen(false);
    setEditingContact(null);
    setFormData({ name: '', phone: '', email: '' });
  };

  // حفظ البيانات
  const saveContact = () => {
    if (!formData.name || !formData.phone) {
      alert('يرجى ملء الحقول المطلوبة');
      return;
    }

    const contactType = activeTab === 'clients' ? 'client' : 'supplier';

    if (editingContact) {
      // تحديث
      setContacts(prev => prev.map(contact => 
        contact.id === editingContact.id 
          ? { ...contact, ...formData }
          : contact
      ));
    } else {
      // إضافة جديد
      const newContact: Contact = {
        id: Date.now(),
        ...formData,
        type: contactType
      };
      setContacts(prev => [...prev, newContact]);
    }

    closeDialog();
  };

  // حذف جهة اتصال
  const deleteContact = (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذه الجهة؟')) {
      setContacts(prev => prev.filter(contact => contact.id !== id));
    }
  };

  // عرض الجدول
  const renderTable = (data: Contact[]) => (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>الاسم</TableHead>
            <TableHead>رقم الهاتف</TableHead>
            <TableHead>البريد الإلكتروني</TableHead>
            <TableHead>الإجراءات</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.length === 0 ? (
            <TableRow>
              <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                لا توجد بيانات للعرض
              </TableCell>
            </TableRow>
          ) : (
            data.map((contact) => (
              <TableRow key={contact.id}>
                <TableCell className="font-medium">{contact.name}</TableCell>
                <TableCell>{contact.phone}</TableCell>
                <TableCell>{contact.email}</TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openDialog(contact)}
                    >
                      تحرير
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => deleteContact(contact.id)}
                    >
                      حذف
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="py-8">
            <LoadingSpinner />
            <p className="text-center mt-4 text-muted-foreground">جار تحميل البيانات...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">العملاء والموردين</h1>
      </div>

      <Tabs 
        value={activeTab} 
        onValueChange={(value: 'clients' | 'suppliers') => setActiveTab(value)}
        className="space-y-4"
      >
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="clients" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              العملاء ({filteredClients.length})
            </TabsTrigger>
            <TabsTrigger value="suppliers" className="flex items-center gap-2">
              <Building className="w-4 h-4" />
              الموردين ({filteredSuppliers.length})
            </TabsTrigger>
          </TabsList>

          <Button onClick={() => openDialog()} className="flex items-center gap-2">
            <Plus className="w-4 h-4" />
            إضافة {activeTab === 'clients' ? 'عميل' : 'مورد'} جديد
          </Button>
        </div>

        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="البحث في الأسماء أو الهواتف أو الإيميلات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <TabsContent value="clients" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                قائمة العملاء
              </CardTitle>
            </CardHeader>
            <CardContent>
              {renderTable(filteredClients)}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="suppliers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="w-5 h-5" />
                قائمة الموردين
              </CardTitle>
            </CardHeader>
            <CardContent>
              {renderTable(filteredSuppliers)}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* نافذة الإضافة/التحرير */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingContact ? 'تحرير' : 'إضافة'} {activeTab === 'clients' ? 'عميل' : 'مورد'}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">الاسم *</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="أدخل اسم الشركة أو الشخص"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">رقم الهاتف *</label>
              <Input
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="05xxxxxxxx"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">البريد الإلكتروني</label>
              <Input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={closeDialog}>
              إلغاء
            </Button>
            <Button onClick={saveContact}>
              {editingContact ? 'تحديث' : 'إضافة'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}