const { PrismaClient } = require('./generated/prisma');

const prisma = new PrismaClient();

async function main() {
  console.log('🔄 اختبار اتصال قاعدة البيانات...');

  try {
    // جلب عدد المستخدمين الحاليين
    const userCount = await prisma.user.count();
    console.log(`📊 عدد المستخدمين الحاليين: ${userCount}`);

    // إنشاء مستخدم جديد مع بريد إلكتروني فريد
    const randomEmail = `user${Date.now()}@example.com`;
    const user = await prisma.user.create({
      data: {
        name: 'مستخدم جديد',
        email: randomEmail,
      },
    });
    console.log('✅ تم إنشاء المستخدم:', user);

    // إنشاء منشور للمستخدم
    const post = await prisma.post.create({
      data: {
        title: 'منشور تجريبي',
        content: 'هذا محتوى تجريبي لاختبار قاعدة البيانات',
        authorId: user.id,
      },
    });
    console.log('✅ تم إنشاء المنشور:', post);

    // جلب جميع المستخدمين مع منشوراتهم
    const usersWithPosts = await prisma.user.findMany({
      include: {
        posts: true,
      },
    });
    console.log('📋 المستخدمون مع منشوراتهم:');
    usersWithPosts.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.email}) - ${user.posts.length} منشور`);
    });

    console.log('🎉 جميع العمليات تمت بنجاح!');

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await prisma.$disconnect();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

main();
