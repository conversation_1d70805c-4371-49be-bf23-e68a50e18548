// Script to clean up test supply orders
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanupTestOrders() {
  console.log('🧹 تنظيف أوامر التوريد الاختبارية...');
  
  try {
    // جلب جميع أوامر التوريد
    const allOrders = await prisma.supplyOrder.findMany({
      orderBy: { id: 'desc' }
    });
    
    console.log(`📋 تم العثور على ${allOrders.length} أمر توريد:`);
    
    allOrders.forEach((order, index) => {
      console.log(`${index + 1}. ${order.supplyOrderId} - ${order.employeeName} - ${new Date(order.createdAt).toLocaleDateString()}`);
    });
    
    // حذف أوامر التوريد الاختبارية (التي تحتوي على أرقام اختبارية أو كلمة "test")
    const testOrders = allOrders.filter(order => 
      order.supplyOrderId.includes('test') || 
      order.supplyOrderId.includes('TEST') ||
      order.employeeName.includes('test') ||
      order.employeeName.includes('TEST') ||
      order.notes?.includes('test') ||
      order.notes?.includes('اختبار')
    );
    
    if (testOrders.length > 0) {
      console.log(`\n🗑️ سيتم حذف ${testOrders.length} أمر اختباري:`);
      testOrders.forEach(order => {
        console.log(`- ${order.supplyOrderId} (${order.employeeName})`);
      });
      
      // حذف الأوامر الاختبارية
      const deleteResult = await prisma.supplyOrder.deleteMany({
        where: {
          id: {
            in: testOrders.map(order => order.id)
          }
        }
      });
      
      console.log(`✅ تم حذف ${deleteResult.count} أمر اختباري`);
    } else {
      console.log('ℹ️ لم يتم العثور على أوامر اختبارية للحذف');
    }
    
    // عرض الأوامر المتبقية
    const remainingOrders = await prisma.supplyOrder.findMany({
      orderBy: { id: 'desc' }
    });
    
    console.log(`\n📋 الأوامر المتبقية (${remainingOrders.length}):`);
    remainingOrders.forEach((order, index) => {
      console.log(`${index + 1}. ${order.supplyOrderId} - ${order.employeeName} - ${new Date(order.createdAt).toLocaleDateString()}`);
    });
    
  } catch (error) {
    console.error('❌ خطأ في تنظيف أوامر التوريد:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanupTestOrders();
