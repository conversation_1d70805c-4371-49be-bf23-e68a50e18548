// Updated maintenance functions for store.tsx

// INSTRUCTIONS FOR MANUAL UPDATE:
// Replace the existing addMaintenanceOrder function in store.tsx (around line 728) with this:

const addMaintenanceOrder = async (
  order: Omit<MaintenanceOrder, 'id' | 'createdAt'> & { id?: number; status?: 'wip' | 'completed' | 'draft' }
) => {
  try {
    const response = await fetch('/api/maintenance-orders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order),
    });

    if (!response.ok) {
      throw new Error('Failed to create maintenance order');
    }

    const newOrder = await response.json();
    setMaintenanceOrders((prev) => [newOrder, ...prev].sort((a, b) => b.id - a.id));

    addActivity({
      type: 'maintenance',
      description: `تم إنشاء أمر صيانة ${newOrder.orderNumber}`,
    });
  } catch (error) {
    console.error('Failed to add maintenance order:', error);
    throw error;
  }
};

// Replace the existing updateMaintenanceOrder function in store.tsx (around line 770) with this:

const updateMaintenanceOrder = async (updatedOrder: MaintenanceOrder) => {
  try {
    const response = await fetch('/api/maintenance-orders', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updatedOrder),
    });

    if (!response.ok) {
      throw new Error('Failed to update maintenance order');
    }

    const order = await response.json();
    setMaintenanceOrders((prev) => prev.map((o) => (o.id === order.id ? order : o)));

    addActivity({
      type: 'maintenance',
      description: `تم تحديث أمر الصيانة ${order.orderNumber}`,
    });
  } catch (error) {
    console.error('Failed to update maintenance order:', error);
    throw error;
  }
};

// Replace the existing deleteMaintenanceOrder function in store.tsx (around line 894) with this:

const deleteMaintenanceOrder = async (orderId: number) => {
  try {
    const response = await fetch('/api/maintenance-orders', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id: orderId }),
    });

    if (!response.ok) {
      throw new Error('Failed to delete maintenance order');
    }

    setMaintenanceOrders((prev) => prev.filter((o) => o.id !== orderId));

    addActivity({
      type: 'maintenance',
      description: `تم حذف أمر الصيانة`,
    });
  } catch (error) {
    console.error('Failed to delete maintenance order:', error);
    throw error;
  }
};

// Replace the existing addMaintenanceReceiptOrder function in store.tsx (around line 1049) with this:

const addMaintenanceReceiptOrder = async (order: Omit<MaintenanceReceiptOrder, 'id' | 'createdAt'>) => {
  try {
    const response = await fetch('/api/maintenance-receipts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order),
    });

    if (!response.ok) {
      throw new Error('Failed to create maintenance receipt');
    }

    const newOrder = await response.json();
    setMaintenanceReceiptOrders((prev) => [newOrder, ...prev].sort((a, b) => b.id - a.id));

    addActivity({
      type: 'maintenance',
      description: `تم إنشاء أمر استلام جديد ${newOrder.receiptNumber} يحتوي على ${newOrder.items.length} جهاز`,
    });
  } catch (error) {
    console.error('Failed to add maintenance receipt order:', error);
    throw error;
  }
};

// Replace the existing updateMaintenanceReceiptOrder function in store.tsx (around line 1067) with this:

const updateMaintenanceReceiptOrder = async (updatedOrder: MaintenanceReceiptOrder) => {
  try {
    const response = await fetch('/api/maintenance-receipts', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updatedOrder),
    });

    if (!response.ok) {
      throw new Error('Failed to update maintenance receipt');
    }

    const order = await response.json();
    setMaintenanceReceiptOrders((prev) => prev.map((o) => (o.id === order.id ? order : o)));

    addActivity({
      type: 'maintenance',
      description: `تم تحديث أمر الاستلام ${order.receiptNumber}`,
    });
  } catch (error) {
    console.error('Failed to update maintenance receipt order:', error);
    throw error;
  }
};

// Replace the existing deleteMaintenanceReceiptOrder function in store.tsx (around line 1151) with this:

const deleteMaintenanceReceiptOrder = async (orderId: number) => {
  try {
    const response = await fetch('/api/maintenance-receipts', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id: orderId }),
    });

    if (!response.ok) {
      throw new Error('Failed to delete maintenance receipt');
    }

    setMaintenanceReceiptOrders((prev) => prev.filter((o) => o.id !== orderId));

    addActivity({
      type: 'maintenance',
      description: `تم حذف أمر الاستلام`,
    });
  } catch (error) {
    console.error('Failed to delete maintenance receipt order:', error);
    throw error;
  }
};

// SUMMARY OF CHANGES NEEDED:

// 1. The loadDataFromAPIs function in store.tsx has already been updated to include maintenance data loading

// 2. You need to manually replace the following functions in store.tsx:
//    - addMaintenanceOrder (around line 728)
//    - updateMaintenanceOrder (around line 770)
//    - deleteMaintenanceOrder (around line 894)
//    - addMaintenanceReceiptOrder (around line 1049)
//    - updateMaintenanceReceiptOrder (around line 1067)
//    - deleteMaintenanceReceiptOrder (around line 1151)

// 3. All the replacement functions are provided above with proper TypeScript types

// 4. The API endpoints are already created and working:
//    - /api/maintenance-orders (GET, POST, PUT, DELETE)
//    - /api/maintenance-receipts (GET, POST, PUT, DELETE)

// 5. The Prisma schema has been updated with MaintenanceOrder and MaintenanceReceiptOrder models

// 6. After making these changes, the maintenance system will use persistent database storage
//    instead of localStorage, ensuring data persistence between sessions.
