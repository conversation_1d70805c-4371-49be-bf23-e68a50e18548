# إصلاح خطأ حذف المرتجعات - TypeError: order.items.some is not a function
## Returns Delete Error Fix - TypeError: order.items.some is not a function

---

## المشكلة

عند محاولة حذف أمر مرتجع، كان يظهر الخطأ التالي:
```
TypeError: order.items.some is not a function
    at checkReturnRelations
    at handleDeleteReturn
```

### السبب الجذري:
في قاعدة البيانات، حقل `items` يتم حفظه كـ **JSON string**، لكن الكود كان يحاول التعامل معه كـ **array** مباشرة.

**مثال على البيانات في قاعدة البيانات**:
```json
{
  "items": "[{\"deviceId\":\"222222222222545\",\"model\":\"Apple iPad Air\",\"returnReason\":\"خلل مصنعي\"}]"
}
```

**الكود القديم (خطأ)**:
```typescript
// ❌ يحاول استخدام .some على string
order.items.some(item => imeisInReturn.includes(item.imei))
```

---

## الإصلاحات المطبقة

### ✅ **1. إصلاح دالة `checkReturnRelations`**

**إصلاح استخراج الأجهزة من المرتجع**:
```typescript
// قبل الإصلاح
const imeisInReturn = returnToCheck.items.map(item => item.deviceId); // ❌ خطأ

// بعد الإصلاح
let imeisInReturn: string[] = [];
try {
  const items = typeof returnToCheck.items === 'string' ? JSON.parse(returnToCheck.items) : returnToCheck.items;
  imeisInReturn = Array.isArray(items) ? items.map(item => item.deviceId) : [];
} catch {
  imeisInReturn = [];
}
```

**إصلاح فحص أوامر التوريد**:
```typescript
// قبل الإصلاح
const relatedSupplyOrders = supplyOrders.filter(order =>
  order.items.some(item => imeisInReturn.includes(item.imei)) // ❌ خطأ
);

// بعد الإصلاح
const relatedSupplyOrders = supplyOrders.filter(order => {
  try {
    const items = typeof order.items === 'string' ? JSON.parse(order.items) : order.items;
    return Array.isArray(items) && items.some(item => imeisInReturn.includes(item.imei));
  } catch {
    return false;
  }
});
```

**إصلاح فحص أوامر التقييم**:
```typescript
// قبل الإصلاح
const relatedEvaluations = evaluationOrders.filter(evalOrder =>
  evalOrder.items.some(item => imeisInReturn.includes(item.deviceId)) // ❌ خطأ
);

// بعد الإصلاح
const relatedEvaluations = evaluationOrders.filter(evalOrder => {
  try {
    const items = typeof evalOrder.items === 'string' ? JSON.parse(evalOrder.items) : evalOrder.items;
    return Array.isArray(items) && items.some(item => imeisInReturn.includes(item.deviceId));
  } catch {
    return false;
  }
});
```

**إصلاح فحص التحويلات المخزنية**:
```typescript
// قبل الإصلاح
const relatedTransfers = warehouseTransfers.filter(transfer =>
  transfer.items.some(item => imeisInReturn.includes(item.deviceId)) // ❌ خطأ
);

// بعد الإصلاح
const relatedTransfers = warehouseTransfers.filter(transfer => {
  try {
    const items = typeof transfer.items === 'string' ? JSON.parse(transfer.items) : transfer.items;
    return Array.isArray(items) && items.some(item => imeisInReturn.includes(item.deviceId));
  } catch {
    return false;
  }
});
```

### ✅ **2. إصلاح دالة `checkDeviceRelations`**

**إصلاح فحص أوامر التوريد للجهاز الفردي**:
```typescript
// قبل الإصلاح
const deviceInSupplyOrders = supplyOrders.some(order =>
  order.items.some(item => item.imei === deviceId) // ❌ خطأ
);

// بعد الإصلاح
const deviceInSupplyOrders = supplyOrders.some(order => {
  try {
    const items = typeof order.items === 'string' ? JSON.parse(order.items) : order.items;
    return Array.isArray(items) && items.some(item => item.imei === deviceId);
  } catch {
    return false;
  }
});
```

**إصلاح فحص أوامر التقييم للجهاز الفردي**:
```typescript
// قبل الإصلاح
const deviceInEvaluations = evaluationOrders.some(evalOrder =>
  evalOrder.items.some(item => item.deviceId === deviceId) // ❌ خطأ
);

// بعد الإصلاح
const deviceInEvaluations = evaluationOrders.some(evalOrder => {
  try {
    const items = typeof evalOrder.items === 'string' ? JSON.parse(evalOrder.items) : evalOrder.items;
    return Array.isArray(items) && items.some(item => item.deviceId === deviceId);
  } catch {
    return false;
  }
});
```

**إصلاح فحص التحويلات المخزنية للجهاز الفردي**:
```typescript
// قبل الإصلاح
const deviceInTransfers = warehouseTransfers.some(transfer =>
  transfer.items.some(item => item.deviceId === deviceId) // ❌ خطأ
);

// بعد الإصلاح
const deviceInTransfers = warehouseTransfers.some(transfer => {
  try {
    const items = typeof transfer.items === 'string' ? JSON.parse(transfer.items) : transfer.items;
    return Array.isArray(items) && items.some(item => item.deviceId === deviceId);
  } catch {
    return false;
  }
});
```

**إصلاح فحص المرتجعات الأخرى**:
```typescript
// قبل الإصلاح
const deviceInOtherReturns = returns.some(returnOrder =>
  returnOrder.id !== loadedReturn?.id &&
  returnOrder.items.some(item => item.deviceId === deviceId) // ❌ خطأ
);

// بعد الإصلاح
const deviceInOtherReturns = returns.some(returnOrder => {
  if (returnOrder.id === loadedReturn?.id) return false;
  try {
    const items = typeof returnOrder.items === 'string' ? JSON.parse(returnOrder.items) : returnOrder.items;
    return Array.isArray(items) && items.some(item => item.deviceId === deviceId);
  } catch {
    return false;
  }
});
```

---

## النتيجة النهائية

### ✅ **المشاكل المحلولة**:
1. **لا مزيد من أخطاء TypeError**: تم إصلاح جميع استدعاءات `.some()` على strings
2. **معالجة آمنة للبيانات**: إضافة `try-catch` لحماية من أخطاء JSON parsing
3. **توافق مع أنواع البيانات المختلفة**: يعمل مع `string` (JSON) و `array` على حد سواء
4. **حذف آمن للمرتجعات**: يمكن الآن حذف المرتجعات بدون أخطاء

### 🔍 **آلية العمل الجديدة**:
1. **فحص نوع البيانات**: `typeof order.items === 'string'`
2. **تحويل JSON إلى Array**: `JSON.parse(order.items)` إذا كان string
3. **التحقق من صحة Array**: `Array.isArray(items)`
4. **معالجة الأخطاء**: إرجاع `false` في حالة فشل التحويل
5. **تطبيق المنطق**: استخدام `.some()` على Array صحيح

### 📊 **الدوال المحدثة**:
- ✅ `checkReturnRelations()` - فحص العلاقات قبل حذف المرتجع
- ✅ `checkDeviceRelations()` - فحص العلاقات للجهاز الفردي
- ✅ جميع فحوصات أوامر التوريد والتقييم والتحويلات المخزنية
- ✅ فحص المرتجعات الأخرى

### 🛡️ **الحماية المضافة**:
- **معالجة أخطاء JSON**: `try-catch` blocks لحماية من بيانات فاسدة
- **فحص نوع البيانات**: التأكد من أن البيانات array قبل استخدام `.some()`
- **قيم افتراضية آمنة**: إرجاع `false` أو `[]` في حالة الخطأ

---

*تم إصلاح خطأ حذف المرتجعات بنجاح مع إضافة حماية شاملة ضد أخطاء أنواع البيانات*
