const { default: fetch } = require('node-fetch');

async function quickTest() {
  const token = 'dXNlcjphZG1pbjphZG1pbg=='; // user:admin:admin

  console.log('🧪 اختبار سريع للـ APIs...');

  try {
    const usersRes = await fetch('http://localhost:9005/api/users', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Users API:', usersRes.status, usersRes.ok ? 'OK' : 'ERROR');

    const settingsRes = await fetch('http://localhost:9005/api/settings', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Settings API:', settingsRes.status, settingsRes.ok ? 'OK' : 'ERROR');

    const clientsRes = await fetch('http://localhost:9005/api/clients', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Clients API:', clientsRes.status, clientsRes.ok ? 'OK' : 'ERROR');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

quickTest();
