// Script مبسط لتحديث وظيفة updateSystemSettings فقط
const fs = require('fs');

const filePath = 'context/store.tsx';
let content = fs.readFileSync(filePath, 'utf8');

console.log('🔄 تحديث وظيفة updateSystemSettings...');

// البحث عن الوظيفة والاستبدال
const oldFunction = `const updateSystemSettings = (settings: SystemSettings) => {
    setSystemSettings(settings);
  };`;

const newFunction = `const updateSystemSettings = async (settings: SystemSettings) => {
    try {
      // إرسال التحديث إلى API
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update system settings');
      }

      // استقبال الإعدادات المحدثة من API
      const updatedSettings = await response.json();

      // تحديث حالة التطبيق
      setSystemSettings(updatedSettings);

      addActivity({
        type: "supply",
        description: "تم تحديث إعدادات النظام بنجاح",
      });
      
      return updatedSettings;
    } catch (error) {
      console.error('Failed to update system settings:', error);
      addActivity({
        type: "supply",
        description: \`⚠️ فشل في تحديث إعدادات النظام: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };`;

content = content.replace(oldFunction, newFunction);

fs.writeFileSync(filePath, content, 'utf8');

console.log('✅ تم تحديث updateSystemSettings بنجاح!');
console.log('الآن إعدادات النظام ستحتفظ بالبيانات في قاعدة البيانات.');
