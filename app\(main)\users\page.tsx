
'use client';

import { useState, useEffect } from 'react';
import { User, AppPermissions, permissionPages } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  UserPlus,
  Search,
  Edit,
  Shield,
  Trash2,
  User<PERSON>he<PERSON>,
  UserX,
  Users,
} from 'lucide-react';

// نسخة مبسطة وآمنة من صفحة إدارة المستخدمين
export default function UsersPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  // إضافة مستخدم افتراضي إذا كانت القائمة فارغة
  useEffect(() => {
    if (!isLoading && users.length === 0) {
      const defaultAdmin: User = {
        id: 1,
        name: 'المدير العام',
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        permissions: permissionPages.reduce(
          (acc, page) => ({
            ...acc,
            [page]: {
              view: true,
              create: true,
              edit: true,
              delete: true,
              viewAll: true,
              manage: [],
              acceptWithoutWarranty: true
            },
          }),
          {} as AppPermissions
        ),
        isActive: true,
        createdAt: new Date(),
        lastLogin: new Date().toISOString()
      };
      setUsers([defaultAdmin]);
    }
  }, [isLoading, users.length]);
  // هنا تبدأ الدالة بشكل صحيح

  const handleAddUser = () => {
    setEditingUser(undefined);
    setFormData({
      name: '',
      username: '',
      email: '',
      role: 'user',
      password: '',
      confirmPassword: ''
    });
    setIsFormOpen(true);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setFormData({
      name: user.name,
      username: user.username,
      email: user.email || '',
      role: user.role,
      password: '',
      confirmPassword: ''
    });
    setIsFormOpen(true);
  };

  const handleSaveUser = async () => {
    try {
      // التحقق من صحة البيانات
      if (!formData.name || !formData.username) {
        toast({
          title: 'خطأ',
          description: 'يرجى ملء جميع الحقول المطلوبة',
          variant: 'destructive',
        });
        return;
      }

      if (!editingUser && (!formData.password || formData.password !== formData.confirmPassword)) {
        toast({
          title: 'خطأ',
          description: 'يرجى التأكد من كلمة المرور',
          variant: 'destructive',
        });
        return;
      }

      // إنشاء أو تحديث المستخدم
      if (editingUser) {
        // تحديث مستخدم موجود
        setUsers(prev => prev.map(user => 
          user.id === editingUser.id 
            ? { 
                ...user, 
                name: formData.name,
                username: formData.username,
                email: formData.email,
                role: formData.role
              }
            : user
        ));
        
        toast({
          title: 'تم بنجاح',
          description: 'تم تحديث المستخدم بنجاح',
        });
      } else {
        // إضافة مستخدم جديد
        const newUser: User = {
          id: Math.max(...users.map(u => u.id)) + 1,
          name: formData.name,
          username: formData.username,
          email: formData.email,
          role: formData.role,
          permissions: initialPermissions,
          isActive: true,
          createdAt: new Date(),
          lastLogin: null
        };
        
        setUsers(prev => [...prev, newUser]);
        
        toast({
          title: 'تم بنجاح',
          description: 'تم إضافة المستخدم بنجاح',
        });
      }

      setIsFormOpen(false);
      
    } catch (error) {
      console.error('خطأ في حفظ المستخدم:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ في حفظ المستخدم',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteUser = (user: User) => {
    setConfirmDialogContent({
      title: 'حذف المستخدم',
      description: `هل أنت متأكد من حذف المستخدم "${user.name}"؟ هذا الإجراء لا يمكن التراجع عنه.`,
    });
    setConfirmAction(() => () => {
      setUsers(prev => prev.filter(u => u.id !== user.id));
      toast({
        title: 'تم بنجاح',
        description: 'تم حذف المستخدم بنجاح',
      });
    });
    setIsConfirmDialogOpen(true);
  };

  const handleToggleUserStatus = (user: User) => {
    const action = user.isActive ? 'تعطيل' : 'تفعيل';
    setConfirmDialogContent({
      title: `${action} المستخدم`,
      description: `هل أنت متأكد من ${action} المستخدم "${user.name}"؟`,
    });
    setConfirmAction(() => () => {
      setUsers(prev => prev.map(u => 
        u.id === user.id ? { ...u, isActive: !u.isActive } : u
      ));
      toast({
        title: 'تم بنجاح',
        description: `تم ${action} المستخدم بنجاح`,
      });
    });
    setIsConfirmDialogOpen(true);
  };

  // تصفية المستخدمين
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && user.isActive) ||
                         (statusFilter === 'inactive' && !user.isActive);
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    
    return matchesSearch && matchesStatus && matchesRole;
  });

  const getRoleBadge = (role: string) => {
    const variants = {
      admin: 'default',
      manager: 'secondary',
      user: 'outline'
    } as const;
    
    const labels = {
      admin: 'مدير',
      manager: 'مشرف',
      user: 'مستخدم'
    };
    
    return (
      <Badge variant={variants[role as keyof typeof variants] || 'outline'}>
        {labels[role as keyof typeof labels] || role}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" />
        <span className="ml-2 text-lg">جاري تحميل المستخدمين...</span>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">إدارة المستخدمين</h1>
        {permissions.create && (
          <Button onClick={handleAddUser}>
            <UserPlus className="h-4 w-4 mr-2" />
            إضافة مستخدم جديد
          </Button>
        )}
      </div>

      {/* شريط البحث والفلاتر */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="البحث بالاسم أو اسم المستخدم أو الإيميل..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="تصفية حسب الحالة" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">جميع الحالات</SelectItem>
            <SelectItem value="active">نشط</SelectItem>
            <SelectItem value="inactive">غير نشط</SelectItem>
          </SelectContent>
        </Select>

        <Select value={roleFilter} onValueChange={setRoleFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="تصفية حسب الدور" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">جميع الأدوار</SelectItem>
            <SelectItem value="admin">مدير</SelectItem>
            <SelectItem value="manager">مشرف</SelectItem>
            <SelectItem value="user">مستخدم</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* جدول المستخدمين */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            قائمة المستخدمين ({filteredUsers.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredUsers.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <Users className="mx-auto h-16 w-16 mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">لا توجد مستخدمين</h3>
              <p className="text-sm mb-4">
                {searchTerm || statusFilter !== 'all' || roleFilter !== 'all'
                  ? 'لا توجد نتائج مطابقة للبحث'
                  : 'ابدأ بإضافة أول مستخدم'
                }
              </p>
              {permissions.create && (
                <Button onClick={handleAddUser}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  إضافة مستخدم جديد
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>المستخدم</TableHead>
                  <TableHead>الدور</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>آخر تسجيل دخول</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-muted-foreground">
                          @{user.username}
                          {user.email && ` • ${user.email}`}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{getRoleBadge(user.role)}</TableCell>
                    <TableCell>
                      <Badge variant={user.isActive ? 'default' : 'secondary'}>
                        {user.isActive ? 'نشط' : 'غير نشط'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {user.lastLogin 
                        ? new Date(user.lastLogin).toLocaleDateString('ar-SA')
                        : 'لم يسجل دخول بعد'
                      }
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        {permissions.edit && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditUser(user)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        )}
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleUserStatus(user)}
                        >
                          {user.isActive ? (
                            <UserX className="h-4 w-4" />
                          ) : (
                            <UserCheck className="h-4 w-4" />
                          )}
                        </Button>
                        
                        {permissions.delete && user.role !== 'admin' && (
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDeleteUser(user)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* نموذج إضافة/تعديل المستخدم */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingUser ? 'تعديل مستخدم' : 'إضافة مستخدم جديد'}
            </DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div>
              <Label htmlFor="name">الاسم *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
              />
            </div>
            <div>
              <Label htmlFor="username">اسم المستخدم *</Label>
              <Input
                id="username"
                value={formData.username}
                onChange={(e) => setFormData({...formData, username: e.target.value})}
              />
            </div>
            <div>
              <Label htmlFor="email">الإيميل</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({...formData, email: e.target.value})}
              />
            </div>
            <div>
              <Label htmlFor="role">الدور</Label>
              <Select 
                value={formData.role} 
                onValueChange={(value) => setFormData({...formData, role: value as any})}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">مستخدم</SelectItem>
                  <SelectItem value="manager">مشرف</SelectItem>
                  <SelectItem value="admin">مدير</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {!editingUser && (
              <>
                <div>
                  <Label htmlFor="password">كلمة المرور *</Label>
                  <Input
                    id="password"
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData({...formData, password: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="confirmPassword">تأكيد كلمة المرور *</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={(e) => setFormData({...formData, confirmPassword: e.target.value})}
                  />
                </div>
              </>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsFormOpen(false)}>
              إلغاء
            </Button>
            <Button onClick={handleSaveUser}>
              {editingUser ? 'تحديث' : 'إضافة'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* مربع حوار التأكيد */}
      <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{confirmDialogContent.title}</DialogTitle>
            <DialogDescription>{confirmDialogContent.description}</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsConfirmDialogOpen(false)}>
              إلغاء
            </Button>
            <Button 
              variant="destructive" 
              onClick={() => {
                confirmAction?.();
                setIsConfirmDialogOpen(false);
              }}
            >
              تأكيد
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );


  // استخراج الأدوار الفريدة للفلترة
  const uniqueRoles = Array.from(
    new Set(users.map((user) => user.role).filter(Boolean))
    );

  // تحضير البيانات للتصدير
  const exportData = filteredUsers.map((user) => ({
    الاسم: user.name,
    'اسم المستخدم': user.username,
    'البريد الإلكتروني': user.email,
    الهاتف: user.phone || '-',
    'الدور الوظيفي': user.role || '-',
    'موقع الفرع': user.branchLocation || '-',
    الحالة: user.status || 'Active',
    'آخر دخول': user.lastLogin ? formatArabicDate(user.lastLogin) : '-',
  }));

  const exportHeaders = [
    'الاسم',
    'اسم المستخدم',
    'البريد الإلكتروني',
    'الهاتف',
    'الدور الوظيفي',
    'موقع الفرع',
    'الحالة',
    'آخر دخول',
  ];

  const handleExportUsers = () => {
    exportToCSV(exportData, exportHeaders, 'users-report');
    toast({
      title: 'تم التصدير بنجاح',
      description: 'تم تصدير بيانات المستخدمين إلى ملف CSV',
    });
  };




  const openConfirmationDialog = (
    title: string,
    description: string,
    action: () => void,
  ) => {
    setConfirmDialogContent({ title, description });
    setConfirmAction(() => action);
    setIsConfirmDialogOpen(true);
  };

  const handleDisableUser = (userToUpdate: User) => {
    updateUser({
      ...userToUpdate,
      status: userToUpdate.status === 'Active' ? 'Inactive' : 'Active',
    });
    toast({
      title: 'تم تحديث حالة المستخدم',
      description: 'تم تحديث حالة المستخدم بنجاح',
    });
  };

  const handleResetPasswordConfirm = () => {
    if (!resetPasswordUser) return;
    if (!newPassword || newPassword !== confirmNewPassword) {
      toast({
        title: 'خطأ',
        description: 'كلمتا المرور غير متطابقتين أو فارغتان.',
        variant: 'destructive',
      });
      return;
    }
    // In a real app, you would make an API call here.
    // For now, we simulate success.
    console.log(
      `Password for ${resetPasswordUser.name} would be changed to ${newPassword}`
    );

    toast({
      title: 'تم إعادة التعيين',
      description: `تمت إعادة تعيين كلمة المرور للمستخدم ${resetPasswordUser.name} بنجاح.`,
    });
    setResetPasswordUser(null);
    setNewPassword('');
    setConfirmNewPassword('');
  };

  return (
    <div className="container mx-auto p-4" dir="rtl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">إدارة المستخدمين</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExportUsers}>
            <Download className="ml-2 h-4 w-4" />
            تصدير البيانات
          </Button>
          <Button onClick={handleAddUser}>
            <UserPlus className="ml-2 h-4 w-4" />
            إضافة مستخدم جديد
          </Button>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              إجمالي المستخدمين
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">نشط</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {users.filter((u) => u.status === 'Active').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">غير نشط</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {users.filter((u) => u.status === 'Inactive').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              الأدوار المختلفة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{uniqueRoles.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* فلاتر البحث */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            فلاتر البحث
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="بحث بالاسم, اسم المستخدم, أو البريد..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="فلترة حسب الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="Active">نشط</SelectItem>
                <SelectItem value="Inactive">غير نشط</SelectItem>
              </SelectContent>
            </Select>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger>
                <SelectValue placeholder="فلترة حسب الدور" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأدوار</SelectItem>
                {uniqueRoles.map((role) =>
                  role ? (
                    <SelectItem key={role} value={role}>
                      {role}
                    </SelectItem>
                  ) : null,
                )}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>الاسم</TableHead>
              <TableHead>اسم المستخدم</TableHead>
              <TableHead>البريد الإلكتروني</TableHead>
              <TableHead>الهاتف</TableHead>
              <TableHead>الدور/المسمى</TableHead>
              <TableHead>موقع الفرع</TableHead>
              <TableHead>الحالة</TableHead>
              <TableHead>آخر دخول</TableHead>
              <TableHead className="text-center">الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredUsers.map((user) => (
              <TableRow key={user.id}>
                <TableCell className="font-medium">{user.name}</TableCell>
                <TableCell>{user.username}</TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>{user.phone || '-'}</TableCell>
                <TableCell>{user.role || '-'}</TableCell>
                <TableCell>{user.branchLocation || '-'}</TableCell>
                <TableCell>
                  <Badge
                    variant={user.status === 'Active' ? 'default' : 'destructive'}
                  >
                    {user.status === 'Active' ? 'نشط' : 'غير نشط'}
                  </Badge>
                </TableCell>
                <TableCell>
                  {user.lastLogin ? formatArabicDate(user.lastLogin) : '-'}
                </TableCell>
                <TableCell className="text-center">
                  <div className="flex items-center justify-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditUser(user)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setResetPasswordUser(user)}
                    >
                      <Shield className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={
                        user.status === 'Active' ? 'destructive' : 'default'
                      }
                      size="sm"
                      onClick={() => handleDisableUser(user)}
                      disabled={user.id === 1}
                    >
                      {user.status === 'Inactive' ? (
                        <UserCheck className="h-4 w-4" />
                      ) : (
                        <UserX className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <UserForm
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSave={handleSaveUser}
        currentUser={editingUser}
      />

      <Dialog
        open={!!resetPasswordUser}
        onOpenChange={() => setResetPasswordUser(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              إعادة تعيين كلمة المرور لـ {resetPasswordUser?.name}
            </DialogTitle>
            <DialogDescription>
              أدخل كلمة المرور الجديدة وقم بتأكيدها.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="new-password">كلمة المرور الجديدة</Label>
              <Input
                id="new-password"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirm-new-password">تأكيد كلمة المرور</Label>
              <Input
                id="confirm-new-password"
                type="password"
                value={confirmNewPassword}
                onChange={(e) => setConfirmNewPassword(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setResetPasswordUser(null)}
            >
              إلغاء
            </Button>
            <Button onClick={handleResetPasswordConfirm}>
              إعادة تعيين
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
