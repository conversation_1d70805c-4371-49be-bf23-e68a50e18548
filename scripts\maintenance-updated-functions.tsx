// هذا الملف يحتوي على وظائف الصيانة والاستلام والتسليم المحدثة
// يجب نسخ هذه الوظائف وإدراجها في ملف context/store.tsx

// ======== وظائف الصيانة ========
const addMaintenanceOrder = async (
  order: Omit<MaintenanceOrder, "id" | "createdAt"> & {
    id?: number;
    status?: "wip" | "completed" | "draft";
  },
) => {
  try {
    // إذا لم يكن هناك رقم أمر، نقوم بإنشاء واحد
    if (!order.orderNumber) {
      const allExisting = [...maintenanceOrders];
      const useId =
        order.id && order.id > 0
          ? order.id
          : Math.max(0, ...allExisting.map((o) => o.id)) + 1;
      order.orderNumber = `MAINT-${useId}`;
    }
    
    // إرسال الأمر إلى API
    const response = await fetch('/api/maintenance-orders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...order,
        status: order.status || 'wip',
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create maintenance order');
    }

    // استقبال الأمر الذي تم إنشاؤه من API
    const newOrder = await response.json();

    // تحديث حالة التطبيق
    setMaintenanceOrders((prev) => [newOrder, ...prev]);

    // إضافة نشاط
    addActivity({
      type: "maintenance",
      description: `تم إنشاء أمر صيانة ${newOrder.orderNumber}.`,
    });
    
    return newOrder;
  } catch (error) {
    console.error('Failed to add maintenance order:', error);
    addActivity({
      type: "maintenance",
      description: `⚠️ فشل في إنشاء أمر صيانة: ${error instanceof Error ? error.message : String(error)}`,
    });
    throw error;
  }
};

const updateMaintenanceOrder = async (updatedOrder: MaintenanceOrder) => {
  try {
    // إرسال الأمر إلى API
    const response = await fetch('/api/maintenance-orders', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updatedOrder),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update maintenance order');
    }

    // استقبال الأمر المحدّث من API
    const savedOrder = await response.json();

    // تحديث حالة التطبيق
    setMaintenanceOrders((prev) =>
      prev.map((o) =>
        o.id === savedOrder.id
          ? savedOrder
          : o
      ),
    );

    // تحديث حالة الأجهزة
    const originalOrder = maintenanceOrders.find(
      (o) => o.id === updatedOrder.id,
    );
    if (originalOrder) {
      const originalDeviceIds = new Set(originalOrder.items.map((i) => i.id));
      const updatedDeviceIds = new Set(updatedOrder.items.map((i) => i.id));

      const removedDeviceIds = [...originalDeviceIds].filter(
        (id) => !updatedDeviceIds.has(id),
      );
      setDevices((prev) =>
        prev.map((device) =>
          removedDeviceIds.includes(device.id)
            ? { ...device, status: "تحتاج صيانة" }
            : device,
        ),
      );
    }

    updatedOrder.items.forEach((item) => {
      updateDeviceStatus(item.id, "قيد الإصلاح");
    });

    // إضافة نشاط
    addActivity({
      type: "maintenance",
      description: `تم تحديث أمر الصيانة ${updatedOrder.orderNumber}`,
    });
    
    return savedOrder;
  } catch (error) {
    console.error('Failed to update maintenance order:', error);
    addActivity({
      type: "maintenance",
      description: `⚠️ فشل في تحديث أمر صيانة: ${error instanceof Error ? error.message : String(error)}`,
    });
    throw error;
  }
};

const deleteMaintenanceOrder = async (orderId: number) => {
  try {
    const orderToDelete = maintenanceOrders.find((o) => o.id === orderId);
    if (!orderToDelete) return;

    const relationCheck = checkMaintenanceOrderRelations(orderId);
    if (!relationCheck.canDelete) {
      throw new Error(
        `لا يمكن حذف أمر الصيانة: ${relationCheck.reason}${relationCheck.relatedOperations ? "\nالعمليات المرتبطة: " + relationCheck.relatedOperations.join(", ") : ""}`,
      );
    }

    // إرسال طلب الحذف إلى API
    const response = await fetch('/api/maintenance-orders', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id: orderId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete maintenance order');
    }

    // تحديث حالة التطبيق
    setMaintenanceOrders((prev) => prev.filter((o) => o.id !== orderId));

    // إضافة نشاط
    addActivity({
      type: "maintenance",
      description: `تم حذف أمر الصيانة ${orderToDelete.orderNumber}`,
    });
  } catch (error) {
    console.error('Failed to delete maintenance order:', error);
    addActivity({
      type: "maintenance",
      description: `⚠️ فشل في حذف أمر صيانة: ${error instanceof Error ? error.message : String(error)}`,
    });
    throw error;
  }
};

// ======== وظائف استلام الصيانة ========
const addMaintenanceReceiptOrder = async (
  order: Omit<MaintenanceReceiptOrder, "id" | "createdAt">,
) => {
  try {
    // إذا لم يكن هناك رقم للاستلام، نقوم بإنشاء واحد
    if (!order.receiptNumber) {
      const maxId = maintenanceReceiptOrders.reduce(
        (max, o) => (o.id > max ? o.id : max),
        0
      );
      order.receiptNumber = `MREC-${maxId + 1}`;
    }
    
    // إرسال الأمر إلى API
    const response = await fetch('/api/maintenance-receipts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create maintenance receipt order');
    }

    // استقبال الأمر الذي تم إنشاؤه من API
    const newOrder = await response.json();

    // تحديث حالة التطبيق
    setMaintenanceReceiptOrders((prev) => [newOrder, ...prev]);

    // إضافة نشاط
    addActivity({
      type: "maintenance",
      description: `تم إنشاء أمر استلام جديد ${newOrder.receiptNumber} يحتوي على ${newOrder.items.length} جهاز`,
    });
    
    return newOrder;
  } catch (error) {
    console.error('Failed to add maintenance receipt order:', error);
    addActivity({
      type: "maintenance",
      description: `⚠️ فشل في إنشاء أمر استلام صيانة: ${error instanceof Error ? error.message : String(error)}`,
    });
    throw error;
  }
};

const updateMaintenanceReceiptOrder = async (
  updatedOrder: MaintenanceReceiptOrder,
) => {
  try {
    // إرسال الأمر إلى API
    const response = await fetch('/api/maintenance-receipts', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updatedOrder),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update maintenance receipt order');
    }

    // استقبال الأمر المحدّث من API
    const savedOrder = await response.json();

    // تحديث حالة التطبيق
    setMaintenanceReceiptOrders((prev) =>
      prev.map((o) =>
        o.id === savedOrder.id
          ? savedOrder
          : o
      ),
    );

    // إضافة نشاط
    addActivity({
      type: "maintenance",
      description: `تم تحديث أمر الاستلام ${updatedOrder.receiptNumber}`,
    });
    
    return savedOrder;
  } catch (error) {
    console.error('Failed to update maintenance receipt order:', error);
    addActivity({
      type: "maintenance",
      description: `⚠️ فشل في تحديث أمر استلام صيانة: ${error instanceof Error ? error.message : String(error)}`,
    });
    throw error;
  }
};

const deleteMaintenanceReceiptOrder = async (orderId: number) => {
  try {
    const orderToDelete = maintenanceReceiptOrders.find((o) => o.id === orderId);
    if (!orderToDelete) return;

    // إرسال طلب الحذف إلى API
    const response = await fetch('/api/maintenance-receipts', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id: orderId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete maintenance receipt order');
    }

    // تحديث حالة التطبيق
    setMaintenanceReceiptOrders((prev) => prev.filter((o) => o.id !== orderId));

    // إضافة نشاط
    addActivity({
      type: "maintenance",
      description: `تم حذف أمر الاستلام ${orderToDelete.receiptNumber}`,
    });
  } catch (error) {
    console.error('Failed to delete maintenance receipt order:', error);
    addActivity({
      type: "maintenance",
      description: `⚠️ فشل في حذف أمر استلام صيانة: ${error instanceof Error ? error.message : String(error)}`,
    });
    throw error;
  }
};

// ======== وظائف التسليم ========
const addDeliveryOrder = async (
  order: Omit<DeliveryOrder, "id" | "createdAt">,
) => {
  try {
    // إذا لم يكن هناك رقم للتسليم، نقوم بإنشاء واحد
    if (!order.deliveryNumber) {
      const maxId = deliveryOrders.reduce(
        (max, o) => (o.id > max ? o.id : max),
        0
      );
      order.deliveryNumber = `DEL-${maxId + 1}`;
    }
    
    // إرسال الأمر إلى API
    const response = await fetch('/api/delivery-orders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create delivery order');
    }

    // استقبال الأمر الذي تم إنشاؤه من API
    const newOrder = await response.json();

    // تحديث حالة التطبيق
    setDeliveryOrders((prev) => [newOrder, ...prev]);

    // إضافة نشاط
    addActivity({
      type: "maintenance",
      description: `تم إنشاء أمر تسليم جديد ${newOrder.deliveryNumber} يحتوي على ${newOrder.items.length} جهاز`,
    });
    
    return newOrder;
  } catch (error) {
    console.error('Failed to add delivery order:', error);
    addActivity({
      type: "maintenance",
      description: `⚠️ فشل في إنشاء أمر تسليم: ${error instanceof Error ? error.message : String(error)}`,
    });
    throw error;
  }
};

const updateDeliveryOrder = async (updatedOrder: DeliveryOrder) => {
  try {
    // إرسال الأمر إلى API
    const response = await fetch('/api/delivery-orders', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updatedOrder),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update delivery order');
    }

    // استقبال الأمر المحدّث من API
    const savedOrder = await response.json();

    // تحديث حالة التطبيق
    setDeliveryOrders((prev) =>
      prev.map((o) =>
        o.id === savedOrder.id
          ? savedOrder
          : o
      ),
    );

    // إضافة نشاط
    addActivity({
      type: "maintenance",
      description: `تم تحديث أمر التسليم ${updatedOrder.deliveryNumber}`,
    });
    
    return savedOrder;
  } catch (error) {
    console.error('Failed to update delivery order:', error);
    addActivity({
      type: "maintenance",
      description: `⚠️ فشل في تحديث أمر تسليم: ${error instanceof Error ? error.message : String(error)}`,
    });
    throw error;
  }
};

const deleteDeliveryOrder = async (orderId: number) => {
  try {
    const orderToDelete = deliveryOrders.find((o) => o.id === orderId);
    if (!orderToDelete) return;

    // إرسال طلب الحذف إلى API
    const response = await fetch('/api/delivery-orders', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id: orderId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete delivery order');
    }

    // تحديث حالة التطبيق
    setDeliveryOrders((prev) => prev.filter((o) => o.id !== orderId));

    // إضافة نشاط
    addActivity({
      type: "maintenance",
      description: `تم حذف أمر التسليم ${orderToDelete.deliveryNumber}`,
    });
  } catch (error) {
    console.error('Failed to delete delivery order:', error);
    addActivity({
      type: "maintenance",
      description: `⚠️ فشل في حذف أمر تسليم: ${error instanceof Error ? error.message : String(error)}`,
    });
    throw error;
  }
};