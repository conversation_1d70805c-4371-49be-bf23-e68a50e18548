# 🐘 دليل الانتقال إلى PostgreSQL وتحديث إعدادات قاعدة البيانات

## 📋 **نظرة عامة:**
هذا الدليل يوضح خطوات الانتقال من SQLite إلى PostgreSQL وتحديث صفحة إعدادات النظام لتطبيق عمليات قاعدة البيانات الحقيقية.

---

## 🎯 **المرحلة الأولى: إعداد PostgreSQL**

### **1.1 تثبيت PostgreSQL**
```bash
# Windows (باستخدام Chocolatey)
choco install postgresql

# أو تحميل من الموقع الرسمي
# https://www.postgresql.org/download/windows/

# التحقق من التثبيت
psql --version
```

### **1.2 إعداد قاعدة البيانات**
```sql
-- الاتصال بـ PostgreSQL
psql -U postgres

-- إنشاء قاعدة بيانات جديدة
CREATE DATABASE deviceflow_db;

-- إنشاء مستخدم للتطبيق
CREATE USER deviceflow_user WITH PASSWORD 'your_secure_password';

-- منح الصلاحيات
GRANT ALL PRIVILEGES ON DATABASE deviceflow_db TO deviceflow_user;

-- الخروج
\q
```

### **1.3 إعداد متغيرات البيئة**
```env
# .env أو .env.local
# قاعدة البيانات الرئيسية
DATABASE_URL="postgresql://deviceflow_user:your_secure_password@localhost:5432/deviceflow_db"

# قواعد بيانات إضافية (اختياري)
MAIN_DB_URL="postgresql://deviceflow_user:your_secure_password@localhost:5432/deviceflow_main"
ARCHIVE_DB_URL="postgresql://deviceflow_user:your_secure_password@localhost:5432/deviceflow_archive"
TEST_DB_URL="postgresql://deviceflow_user:your_secure_password@localhost:5432/deviceflow_test"

# معلومات قاعدة البيانات للواجهة
DB_HOST="localhost"
DB_PORT="5432"
DB_USER="deviceflow_user"
DB_PASSWORD="your_secure_password"
DB_NAME="deviceflow_db"
```

---

## 🔄 **المرحلة الثانية: تحديث Prisma Schema**

### **2.1 تحديث مصدر البيانات**
```prisma
// prisma/schema.prisma

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// باقي النماذج تبقى كما هي مع تعديلات بسيطة...

model User {
  id              Int      @id @default(autoincrement())
  email           String   @unique
  name            String?
  username        String?  @default("user")
  role            String?  @default("user")
  phone           String?  @default("")
  photo           String?  @default("")
  status          String?  @default("Active")
  lastLogin       String?
  branchLocation  String?
  warehouseAccess Json?
  permissions     Json?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  posts           Post[]
  
  @@map("users")
}

model SystemSetting {
  id              Int      @id @default(1)
  logoUrl         String   @default("")
  companyNameAr   String   @default("")
  companyNameEn   String   @default("")
  addressAr       String   @default("")
  addressEn       String   @default("")
  phone           String   @default("")
  email           String   @default("")
  website         String   @default("")
  footerTextAr    String   @default("")
  footerTextEn    String   @default("")
  updatedAt       DateTime @updatedAt
  createdAt       DateTime @default(now())
  
  @@map("system_settings")
}

// إضافة نماذج جديدة لإدارة قواعد البيانات
model DatabaseConnection {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  host        String
  port        Int      @default(5432)
  database    String
  username    String
  password    String   // مشفر
  isActive    Boolean  @default(false)
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  backups     DatabaseBackup[]
  
  @@map("database_connections")
}

model DatabaseBackup {
  id           Int                @id @default(autoincrement())
  name         String
  description  String?
  filePath     String
  fileSize     String
  backupType   String             @default("manual") // manual, automatic
  status       String             @default("completed") // pending, completed, failed
  createdBy    String?
  createdAt    DateTime           @default(now())
  
  connection   DatabaseConnection @relation(fields: [connectionId], references: [id])
  connectionId Int
  
  @@map("database_backups")
}

// باقي النماذج الموجودة...
```

### **2.2 تطبيق التغييرات**
```bash
# تنظيف قاعدة البيانات السابقة (اختياري)
rm -rf prisma/migrations

# إنشاء migration جديد
npx prisma migrate dev --name init-postgresql

# أو إجبار التحديث
npx prisma db push --force-reset

# توليد Prisma Client
npx prisma generate
```

---

## 🛠️ **المرحلة الثالثة: إنشاء APIs لإدارة قواعد البيانات**

### **3.1 API لإدارة الاتصالات**
```typescript
// app/api/database/connections/route.ts

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';

export async function GET() {
  try {
    const connections = await prisma.databaseConnection.findMany({
      select: {
        id: true,
        name: true,
        host: true,
        port: true,
        database: true,
        username: true,
        isActive: true,
        isDefault: true,
        createdAt: true,
        // لا نعرض كلمة المرور
      },
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json(connections);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch connections' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(data.password, 10);
    
    // إذا كان هذا الاتصال الافتراضي، قم بإلغاء الافتراضي للآخرين
    if (data.isDefault) {
      await prisma.databaseConnection.updateMany({
        where: { isDefault: true },
        data: { isDefault: false }
      });
    }

    const connection = await prisma.databaseConnection.create({
      data: {
        name: data.name,
        host: data.host,
        port: data.port,
        database: data.database,
        username: data.username,
        password: hashedPassword,
        isActive: data.isActive || false,
        isDefault: data.isDefault || false,
      }
    });

    return NextResponse.json({
      ...connection,
      password: undefined // لا نعيد كلمة المرور
    }, { status: 201 });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to create connection' },
      { status: 500 }
    );
  }
}
```

### **3.2 API للنسخ الاحتياطي**
```typescript
// app/api/database/backup/route.ts

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import fs from 'fs';

const execAsync = promisify(exec);

export async function POST(request: Request) {
  try {
    const { connectionId, name, description } = await request.json();
    
    // الحصول على معلومات الاتصال
    const connection = await prisma.databaseConnection.findUnique({
      where: { id: connectionId }
    });

    if (!connection) {
      return NextResponse.json(
        { error: 'Connection not found' },
        { status: 404 }
      );
    }

    // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
    const backupDir = path.join(process.cwd(), 'backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    // إنشاء اسم الملف
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `${connection.database}_${timestamp}.sql`;
    const filePath = path.join(backupDir, fileName);

    // فك تشفير كلمة المرور (في الواقع ستحتاج لمكتبة فك تشفير)
    const password = connection.password; // مؤقتاً

    // تنفيذ أمر pg_dump
    const dumpCommand = `pg_dump -h ${connection.host} -p ${connection.port} -U ${connection.username} -d ${connection.database} -f "${filePath}"`;
    
    // تعيين متغير البيئة لكلمة المرور
    const env = { ...process.env, PGPASSWORD: password };

    await execAsync(dumpCommand, { env });

    // الحصول على حجم الملف
    const stats = fs.statSync(filePath);
    const fileSize = `${(stats.size / 1024 / 1024).toFixed(2)} MB`;

    // حفظ معلومات النسخة الاحتياطية في قاعدة البيانات
    const backup = await prisma.databaseBackup.create({
      data: {
        name: name || `نسخة احتياطية - ${new Date().toLocaleDateString('ar-SA')}`,
        description,
        filePath,
        fileSize,
        backupType: 'manual',
        status: 'completed',
        connectionId,
        createdBy: 'النظام', // يمكن تمرير اسم المستخدم
      }
    });

    return NextResponse.json(backup, { status: 201 });
  } catch (error) {
    console.error('Backup error:', error);
    return NextResponse.json(
      { error: 'Failed to create backup' },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const connectionId = searchParams.get('connectionId');

    const where = connectionId ? { connectionId: parseInt(connectionId) } : {};

    const backups = await prisma.databaseBackup.findMany({
      where,
      include: {
        connection: {
          select: {
            name: true,
            database: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json(backups);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch backups' },
      { status: 500 }
    );
  }
}
```

### **3.3 API للاستعادة**
```typescript
// app/api/database/restore/route.ts

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';

const execAsync = promisify(exec);

export async function POST(request: Request) {
  try {
    const { backupId, targetConnectionId } = await request.json();
    
    // الحصول على معلومات النسخة الاحتياطية
    const backup = await prisma.databaseBackup.findUnique({
      where: { id: backupId },
      include: { connection: true }
    });

    if (!backup) {
      return NextResponse.json(
        { error: 'Backup not found' },
        { status: 404 }
      );
    }

    // الحصول على معلومات الاتصال المستهدف
    const targetConnection = await prisma.databaseConnection.findUnique({
      where: { id: targetConnectionId }
    });

    if (!targetConnection) {
      return NextResponse.json(
        { error: 'Target connection not found' },
        { status: 404 }
      );
    }

    // التحقق من وجود ملف النسخة الاحتياطية
    if (!fs.existsSync(backup.filePath)) {
      return NextResponse.json(
        { error: 'Backup file not found' },
        { status: 404 }
      );
    }

    // تنفيذ أمر psql للاستعادة
    const restoreCommand = `psql -h ${targetConnection.host} -p ${targetConnection.port} -U ${targetConnection.username} -d ${targetConnection.database} -f "${backup.filePath}"`;
    
    const env = { ...process.env, PGPASSWORD: targetConnection.password };

    await execAsync(restoreCommand, { env });

    return NextResponse.json({ 
      message: 'Database restored successfully',
      backup: backup.name,
      target: targetConnection.name
    });
  } catch (error) {
    console.error('Restore error:', error);
    return NextResponse.json(
      { error: 'Failed to restore database' },
      { status: 500 }
    );
  }
}
```

---

## 🎨 **المرحلة الرابعة: تحديث صفحة الإعدادات**

### **4.1 مكون إدارة قواعد البيانات الجديد**
```typescript
// components/database-management.tsx

'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  Database,
  Plus,
  Download,
  Upload,
  Trash2,
  Settings,
  Check,
  AlertTriangle,
} from 'lucide-react';

interface DatabaseConnection {
  id: number;
  name: string;
  host: string;
  port: number;
  database: string;
  username: string;
  isActive: boolean;
  isDefault: boolean;
  createdAt: string;
}

interface DatabaseBackup {
  id: number;
  name: string;
  description?: string;
  fileSize: string;
  createdAt: string;
  connection: {
    name: string;
    database: string;
  };
}

export function DatabaseManagement() {
  const [connections, setConnections] = useState<DatabaseConnection[]>([]);
  const [backups, setBackups] = useState<DatabaseBackup[]>([]);
  const [selectedConnection, setSelectedConnection] = useState<number | null>(null);
  const [isCreatingBackup, setIsCreatingBackup] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);
  const { toast } = useToast();

  const [newConnection, setNewConnection] = useState({
    name: '',
    host: 'localhost',
    port: 5432,
    database: '',
    username: '',
    password: '',
    isDefault: false,
  });

  // تحميل قواعد البيانات المتاحة
  useEffect(() => {
    loadConnections();
    loadBackups();
  }, []);

  const loadConnections = async () => {
    try {
      const response = await fetch('/api/database/connections');
      if (response.ok) {
        const data = await response.json();
        setConnections(data);
        if (data.length > 0 && !selectedConnection) {
          setSelectedConnection(data.find((c: DatabaseConnection) => c.isDefault)?.id || data[0].id);
        }
      }
    } catch (error) {
      console.error('Failed to load connections:', error);
    }
  };

  const loadBackups = async () => {
    try {
      const response = await fetch('/api/database/backup');
      if (response.ok) {
        const data = await response.json();
        setBackups(data);
      }
    } catch (error) {
      console.error('Failed to load backups:', error);
    }
  };

  const createConnection = async () => {
    try {
      const response = await fetch('/api/database/connections', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newConnection),
      });

      if (response.ok) {
        toast({
          title: 'تم إنشاء الاتصال',
          description: `تم إنشاء اتصال جديد: ${newConnection.name}`,
        });
        await loadConnections();
        setNewConnection({
          name: '',
          host: 'localhost',
          port: 5432,
          database: '',
          username: '',
          password: '',
          isDefault: false,
        });
      } else {
        throw new Error('Failed to create connection');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'فشل في إنشاء الاتصال',
        variant: 'destructive',
      });
    }
  };

  const createBackup = async () => {
    if (!selectedConnection) return;

    setIsCreatingBackup(true);
    try {
      const response = await fetch('/api/database/backup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          connectionId: selectedConnection,
          name: `نسخة احتياطية - ${new Date().toLocaleDateString('ar-SA')}`,
        }),
      });

      if (response.ok) {
        toast({
          title: 'تم إنشاء النسخة الاحتياطية',
          description: 'تم أخذ نسخة احتياطية بنجاح',
        });
        await loadBackups();
      } else {
        throw new Error('Failed to create backup');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'فشل في إنشاء النسخة الاحتياطية',
        variant: 'destructive',
      });
    } finally {
      setIsCreatingBackup(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* قائمة الاتصالات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            اتصالات قواعد البيانات
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {connections.map((conn) => (
            <div
              key={conn.id}
              className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                selectedConnection === conn.id
                  ? 'border-primary bg-primary/5'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setSelectedConnection(conn.id)}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">{conn.name}</h4>
                  <p className="text-sm text-gray-500">
                    {conn.host}:{conn.port}/{conn.database}
                  </p>
                </div>
                <div className="flex gap-2">
                  {conn.isDefault && (
                    <Badge variant="default">افتراضي</Badge>
                  )}
                  {conn.isActive && (
                    <Badge variant="secondary">
                      <Check className="h-3 w-3 mr-1" />
                      متصل
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* إضافة اتصال جديد */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            إضافة اتصال جديد
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>اسم الاتصال</Label>
              <Input
                value={newConnection.name}
                onChange={(e) => setNewConnection({...newConnection, name: e.target.value})}
                placeholder="قاعدة البيانات الرئيسية"
              />
            </div>
            <div className="space-y-2">
              <Label>الخادم</Label>
              <Input
                value={newConnection.host}
                onChange={(e) => setNewConnection({...newConnection, host: e.target.value})}
                placeholder="localhost"
              />
            </div>
            <div className="space-y-2">
              <Label>المنفذ</Label>
              <Input
                type="number"
                value={newConnection.port}
                onChange={(e) => setNewConnection({...newConnection, port: parseInt(e.target.value)})}
                placeholder="5432"
              />
            </div>
            <div className="space-y-2">
              <Label>اسم قاعدة البيانات</Label>
              <Input
                value={newConnection.database}
                onChange={(e) => setNewConnection({...newConnection, database: e.target.value})}
                placeholder="deviceflow_db"
              />
            </div>
            <div className="space-y-2">
              <Label>اسم المستخدم</Label>
              <Input
                value={newConnection.username}
                onChange={(e) => setNewConnection({...newConnection, username: e.target.value})}
                placeholder="deviceflow_user"
              />
            </div>
            <div className="space-y-2">
              <Label>كلمة المرور</Label>
              <Input
                type="password"
                value={newConnection.password}
                onChange={(e) => setNewConnection({...newConnection, password: e.target.value})}
                placeholder="••••••••"
              />
            </div>
          </div>
          <Button onClick={createConnection} className="w-full">
            إضافة الاتصال
          </Button>
        </CardContent>
      </Card>

      {/* إدارة النسخ الاحتياطية */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            النسخ الاحتياطية
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button
              onClick={createBackup}
              disabled={!selectedConnection || isCreatingBackup}
            >
              {isCreatingBackup ? 'جاري النسخ...' : 'أخذ نسخة احتياطية'}
            </Button>
          </div>
          
          <div className="space-y-2">
            {backups.map((backup) => (
              <div key={backup.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <h4 className="font-medium">{backup.name}</h4>
                  <p className="text-sm text-gray-500">
                    {backup.connection.name} • {backup.fileSize} • 
                    {new Date(backup.createdAt).toLocaleDateString('ar-SA')}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button size="sm" variant="outline">
                    <Upload className="h-4 w-4" />
                    استعادة
                  </Button>
                  <Button size="sm" variant="outline">
                    <Download className="h-4 w-4" />
                    تحميل
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

---

## 📝 **المرحلة الخامسة: تحديث صفحة الإعدادات الرئيسية**

الآن سأنشئ الملف المحدث لصفحة الإعدادات...

---

*سيتم متابعة الدليل في الجزء التالي...*
