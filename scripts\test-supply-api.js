// اختبار API أوامر التوريد
const userToken = Buffer.from('user:admin:admin').toString('base64');

async function testSupplyAPI() {
  console.log('🧪 اختبار API أوامر التوريد...\n');

  try {
    // 1. اختبار جلب أوامر التوريد
    console.log('1️⃣ اختبار جلب أوامر التوريد...');
    const getResponse = await fetch('http://localhost:9005/api/supply', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`   GET /api/supply: ${getResponse.status} ${getResponse.statusText}`);
    if (getResponse.ok) {
      const orders = await getResponse.json();
      console.log(`   ✅ تم العثور على ${orders.length} أمر توريد`);
    } else {
      const error = await getResponse.text();
      console.log(`   ❌ خطأ: ${error}`);
    }

    // 2. اختبار إنشاء أمر توريد جديد
    console.log('\n2️⃣ اختبار إنشاء أمر توريد جديد...');
    
    const testOrder = {
      supplierId: 1,
      invoiceNumber: `INV-${Date.now()}`,
      supplyDate: new Date().toISOString(),
      warehouseId: 1,
      employeeName: 'admin',
      items: [
        {
          manufacturer: 'Samsung',
          model: 'Galaxy S21',
          imei: `TEST${Date.now()}`,
          condition: 'جديد',
          quantity: 1
        }
      ],
      notes: 'اختبار أمر التوريد',
      status: 'completed'
    };

    const postResponse = await fetch('http://localhost:9005/api/supply', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testOrder)
    });

    console.log(`   POST /api/supply: ${postResponse.status} ${postResponse.statusText}`);
    
    if (postResponse.ok) {
      const result = await postResponse.json();
      console.log(`   ✅ تم إنشاء أمر التوريد: ${result.supplyOrderId}`);
      console.log(`   📋 التفاصيل: ${JSON.stringify(result, null, 2)}`);
    } else {
      const error = await postResponse.text();
      console.log(`   ❌ فشل: ${error}`);
    }

    console.log('\n🎉 انتهى اختبار API التوريد!');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

testSupplyAPI();
