# إصلاحات صفحة المرتجعات - رقم المرتجع الرسمي
## Returns Page Fixes - Official Return Number

---

## المشاكل المحلولة

### 🔢 **المشكلة الأولى: خطأ في حفظ رقم المرتجع الرسمي**
**الوصف**: 
```
Failed to create return: Error [PrismaClientValidationError]:
Argument `opReturnNumber` must not be null.
```

**السبب**: 
```typescript
// الكود القديم - مشكلة في API
opReturnNumber: newReturn.opReturnNumber || null, // ❌ يرسل null
```
المشكلة أن حقل `opReturnNumber` مطلوب في قاعدة البيانات لكن كان يتم إرسال `null`.

### 🔄 **المشكلة الثانية: عدم الاحتفاظ برقم المرتجع الرسمي**
**الوصف**: مثل مشكلة صفحة المبيعات، كان رقم المرتجع الرسمي يتم مسحه عند إنشاء مرتجع جديد

---

## الإصلاحات المطبقة

### ✅ **1. إصلاح API المرتجعات**

**في ملف `app/api/returns/route.ts`**:
```typescript
// قبل الإصلاح
opReturnNumber: newReturn.opReturnNumber || null, // ❌ مشكلة - يرسل null

// بعد الإصلاح
opReturnNumber: newReturn.opReturnNumber && newReturn.opReturnNumber.trim() !== '' 
  ? newReturn.opReturnNumber 
  : roNumber, // ✅ يستخدم roNumber كقيمة افتراضية
```

**النتيجة**: 
- ✅ إذا أدخل المستخدم رقماً رسمياً، يتم حفظه
- ✅ إذا لم يدخل رقماً، يتم استخدام `roNumber` (مثل `RO-123`) كقيمة افتراضية
- ✅ لا يتم إرسال `null` أبداً

### ✅ **2. إصلاح الاحتفاظ برقم المرتجع الرسمي**

**تعديل دالة `resetPage`**:
```typescript
// قبل الإصلاح
const resetPage = () => {
  setFormState(initialFormState); // ❌ يمسح كل شيء
  // ...
};

// بعد الإصلاح
const resetPage = (preserveOpReturnNumber = false) => {
  // ✅ الحفاظ على opReturnNumber إذا طُلب ذلك
  const currentOpReturnNumber = preserveOpReturnNumber ? formState.opReturnNumber : '';
  
  setFormState({
    ...initialFormState,
    opReturnNumber: currentOpReturnNumber, // ✅ الحفاظ على الرقم
  });
  // ...
};
```

**تحديث دالة `handleCreateNew`**:
```typescript
// قبل الإصلاح
const handleCreateNew = () => {
  resetPage(); // ❌ يمسح الرقم
  setIsCreateMode(true);
};

// بعد الإصلاح
const handleCreateNew = () => {
  // ✅ الحفاظ على opReturnNumber عند إنشاء مرتجع جديد
  const hasOpReturnNumber = formState.opReturnNumber && formState.opReturnNumber.trim() !== '';
  resetPage(hasOpReturnNumber); // ✅ تمرير معامل للحفاظ على الرقم
  setIsCreateMode(true);
};
```

**تحديث دالة `handleSaveReturn`**:
```typescript
// قبل الإصلاح
resetPage(); // ❌ يمسح الرقم بعد الحفظ

// بعد الإصلاح
// ✅ الاحتفاظ برقم المرتجع الرسمي إذا كان موجوداً
const shouldPreserveOpReturnNumber = formState.opReturnNumber && formState.opReturnNumber.trim() !== '';
resetPage(shouldPreserveOpReturnNumber);
```

### ✅ **3. تحسين واجهة المستخدم**

**إضافة مؤشر بصري وزر مسح**:
```jsx
<div className="space-y-2">
  <div className="flex gap-2">
    <Input
      id="op-return-number"
      placeholder="أدخل الرقم الرسمي..."
      value={formState.opReturnNumber}
      onChange={(e) =>
        setFormState((s) => ({ ...s, opReturnNumber: e.target.value }))
      }
      className="h-8 text-xs flex-1"
    />
    {/* ✅ زر مسح رقم المرتجع الرسمي */}
    {formState.opReturnNumber && (isCreateMode || loadedReturn) && (
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={() => setFormState({ ...formState, opReturnNumber: '' })}
        className="h-8 px-3 text-xs"
        title="مسح رقم المرتجع الرسمي"
      >
        ✕
      </Button>
    )}
  </div>
  {/* ✅ مؤشر بصري للاحتفاظ برقم المرتجع الرسمي */}
  {formState.opReturnNumber && (
    <div className="flex items-center space-x-2 text-xs text-green-600 dark:text-green-400">
      <span>✅ سيتم الاحتفاظ برقم المرتجع الرسمي عند إنشاء مرتجع جديد</span>
    </div>
  )}
</div>
```

---

## النتيجة النهائية

### 🎯 **حل مشكلة الخطأ**:
- ✅ **لا مزيد من أخطاء قاعدة البيانات**: تم إصلاح خطأ `opReturnNumber must not be null`
- ✅ **قيم افتراضية صحيحة**: يستخدم `roNumber` إذا لم يدخل المستخدم رقماً رسمياً
- ✅ **حفظ صحيح**: الأرقام المدخلة تُحفظ بشكل صحيح

### 🔄 **الاحتفاظ برقم المرتجع الرسمي**:
- ✅ **يحتفظ بالرقم**: عند إنشاء مرتجعات متتالية
- ✅ **يحتفظ بعد الحفظ**: الرقم لا يختفي بعد حفظ المرتجع
- ✅ **مؤشر بصري**: يوضح للمستخدم أن الرقم سيتم الاحتفاظ به
- ✅ **زر مسح**: للتحكم في إزالة الرقم عند الحاجة

### 📋 **حالات الاستخدام**:

**مرتجعات متتالية بنفس الرقم الرسمي**:
- المستخدم يدخل الرقم مرة واحدة ويستمر في إنشاء مرتجعات
- مفيد للمرتجعات المرتبطة بنفس الطلبية أو الدفعة

**مرتجعات بدون رقم رسمي**:
- إذا لم يدخل المستخدم رقماً، يستخدم النظام `roNumber` تلقائياً
- لا توجد أخطاء في قاعدة البيانات

**تغيير الرقم الرسمي**:
- المستخدم يمكنه تعديل الرقم أو مسحه باستخدام الزر
- مرونة كاملة في التحكم

### 🔗 **التوافق مع صفحة المبيعات**:
- ✅ **نفس السلوك**: الاحتفاظ بالأرقام الرسمية في كلا الصفحتين
- ✅ **نفس واجهة المستخدم**: مؤشر بصري وزر مسح متطابقان
- ✅ **نفس المنطق**: استخدام نفس آلية `preserveOpNumber/preserveOpReturnNumber`

---

*تم إصلاح جميع مشاكل المرتجعات بنجاح مع توحيد السلوك مع صفحة المبيعات*
