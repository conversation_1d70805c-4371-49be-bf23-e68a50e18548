# 🔗 دليل ربط الصفحات مباشرة بقاعدة البيانات

## 📋 **نظرة عامة:**
هذا الدليل يوضح الطريقة المُتبعة في المشروع لربط أي صفحة مباشرة بقاعدة البيانات باستخدام **Prisma ORM** و **Next.js API Routes**.

---

## 🏗️ **الهيكل العام للنظام:**

```
📁 المشروع/
├── 📁 prisma/
│   └── schema.prisma          # تعريف النماذج
├── 📁 app/api/
│   └── [entity]/
│       └── route.ts           # API endpoints
├── 📁 context/
│   └── store.tsx              # Global State Management
├── 📁 app/(main)/
│   └── [page]/
│       └── page.tsx           # UI Components
└── 📁 lib/
    ├── prisma.ts              # Prisma Client
    └── types.ts               # TypeScript Types
```

---

## 🎯 **خطوات ربط أي صفحة بقاعدة البيانات:**

### **المرحلة 1: إعداد النموذج في قاعدة البيانات**

#### **1.1 تعريف النموذج في Prisma Schema**
```prisma
// prisma/schema.prisma

model EntityName {
  id          Int       @id @default(autoincrement())
  name        String
  description String?
  status      String    @default("active")
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // إضافة حقول حسب الحاجة
  userId      Int?
  user        User?     @relation(fields: [userId], references: [id])
  
  @@map("entity_name") // اسم الجدول في قاعدة البيانات
}
```

#### **1.2 تحديث قاعدة البيانات**
```bash
# في Terminal
npx prisma db push
# أو
npx prisma migrate dev --name add-entity-name
```

#### **1.3 إضافة النوع في TypeScript**
```typescript
// lib/types.ts

export interface EntityName {
  id: number;
  name: string;
  description?: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  userId?: number;
  user?: User;
}
```

---

### **المرحلة 2: إنشاء API Routes**

#### **2.1 إنشاء ملف API Route**
```typescript
// app/api/entity-name/route.ts

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// دالة تسجيل الأنشطة
async function logAudit(
  operation: string,
  details: string,
  userId: number,
  username: string,
) {
  try {
    await prisma.auditLog.create({
      data: {
        userId,
        username,
        operation,
        details,
      }
    });
  } catch (error) {
    console.error('Failed to log audit entry:', error);
  }
}

// GET - قراءة جميع العناصر
export async function GET() {
  try {
    const entities = await prisma.entityName.findMany({
      include: {
        user: true, // تضمين بيانات المستخدم
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json(entities);
  } catch (error) {
    console.error('Failed to fetch entities:', error);
    return NextResponse.json(
      { error: 'Failed to fetch entities' },
      { status: 500 }
    );
  }
}

// POST - إضافة عنصر جديد
export async function POST(request: Request) {
  try {
    const newEntity = await request.json();

    // التحقق من البيانات المطلوبة
    if (!newEntity.name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // إنشاء العنصر الجديد
    const entity = await prisma.entityName.create({
      data: {
        name: newEntity.name,
        description: newEntity.description,
        status: newEntity.status || 'active',
        userId: newEntity.userId,
      },
      include: {
        user: true,
      },
    });

    // تسجيل النشاط
    await logAudit(
      'Entity Added',
      `New entity ${entity.name} added.`,
      entity.userId || 0,
      newEntity.username || 'System',
    );

    return NextResponse.json(entity, { status: 201 });
  } catch (error) {
    console.error('Failed to create entity:', error);
    return NextResponse.json(
      { error: 'Failed to create entity' },
      { status: 500 }
    );
  }
}

// PUT - تحديث عنصر موجود
export async function PUT(request: Request) {
  try {
    const updatedEntity = await request.json();

    if (!updatedEntity.id) {
      return NextResponse.json(
        { error: 'Entity ID is required' },
        { status: 400 }
      );
    }

    // التحقق من وجود العنصر
    const existingEntity = await prisma.entityName.findUnique({
      where: { id: updatedEntity.id }
    });

    if (!existingEntity) {
      return NextResponse.json(
        { error: 'Entity not found' },
        { status: 404 }
      );
    }

    // تحديث العنصر
    const entity = await prisma.entityName.update({
      where: { id: updatedEntity.id },
      data: {
        name: updatedEntity.name || existingEntity.name,
        description: updatedEntity.description !== undefined ? 
          updatedEntity.description : existingEntity.description,
        status: updatedEntity.status || existingEntity.status,
        userId: updatedEntity.userId !== undefined ? 
          updatedEntity.userId : existingEntity.userId,
      },
      include: {
        user: true,
      },
    });

    // تسجيل النشاط
    await logAudit(
      'Entity Updated',
      `Entity ${entity.name} (${entity.id}) updated.`,
      entity.userId || 0,
      updatedEntity.username || 'System',
    );

    return NextResponse.json(entity);
  } catch (error) {
    console.error('Failed to update entity:', error);
    return NextResponse.json(
      { error: 'Failed to update entity' },
      { status: 500 }
    );
  }
}

// DELETE - حذف عنصر
export async function DELETE(request: Request) {
  try {
    const { id, username } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Entity ID is required' },
        { status: 400 }
      );
    }

    // التحقق من وجود العنصر
    const existingEntity = await prisma.entityName.findUnique({
      where: { id }
    });

    if (!existingEntity) {
      return NextResponse.json(
        { error: 'Entity not found' },
        { status: 404 }
      );
    }

    // حذف العنصر
    await prisma.entityName.delete({
      where: { id }
    });

    // تسجيل النشاط
    await logAudit(
      'Entity Deleted',
      `Entity ${existingEntity.name} (${existingEntity.id}) deleted.`,
      existingEntity.userId || 0,
      username || 'System',
    );

    return NextResponse.json({ 
      message: 'Entity deleted successfully' 
    });
  } catch (error) {
    console.error('Failed to delete entity:', error);
    return NextResponse.json(
      { error: 'Failed to delete entity' },
      { status: 500 }
    );
  }
}
```

---

### **المرحلة 3: تحديث Context Store**

#### **3.1 إضافة State للكيان الجديد**
```typescript
// context/store.tsx

interface StoreState {
  // ... الحالات الموجودة
  entities: EntityName[];
  isLoading: boolean;
}

interface StoreActions {
  // ... الوظائف الموجودة
  loadEntities: () => Promise<void>;
  addEntity: (entity: Omit<EntityName, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateEntity: (entity: EntityName) => Promise<void>;
  deleteEntity: (id: number) => Promise<void>;
}
```

#### **3.2 إنشاء وظائف CRUD**
```typescript
// context/store.tsx

export function StoreProvider({ children }: { children: React.ReactNode }) {
  // ... الحالات الموجودة
  const [entities, setEntities] = useState<EntityName[]>([]);

  // تحميل العناصر من API
  const loadEntities = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/entity-name");
      
      if (!response.ok) {
        throw new Error("Failed to fetch entities");
      }
      
      const data = await response.json();
      setEntities(data);
    } catch (error) {
      console.error("Failed to load entities:", error);
      toast({
        title: "خطأ",
        description: "فشل في تحميل البيانات",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // إضافة عنصر جديد
  const addEntity = async (newEntity: Omit<EntityName, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const response = await fetch("/api/entity-name", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...newEntity,
          username: currentUser?.name || "Unknown User",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to add entity");
      }

      const entity = await response.json();
      setEntities((prev) => [entity, ...prev]);

      // تسجيل النشاط محلياً
      addActivity({
        type: "entity",
        description: `تم إضافة ${entity.name}`,
      });

      toast({
        title: "تم بنجاح",
        description: `تم إضافة ${entity.name}`,
      });
    } catch (error) {
      console.error("Failed to add entity:", error);
      toast({
        title: "خطأ",
        description: "فشل في إضافة العنصر",
        variant: "destructive",
      });
      throw error;
    }
  };

  // تحديث عنصر موجود
  const updateEntity = async (updatedEntity: EntityName) => {
    try {
      const response = await fetch("/api/entity-name", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...updatedEntity,
          username: currentUser?.name || "Unknown User",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update entity");
      }

      const entity = await response.json();
      setEntities((prev) => prev.map((e) => (e.id === entity.id ? entity : e)));

      addActivity({
        type: "entity",
        description: `تم تحديث ${entity.name}`,
      });

      toast({
        title: "تم بنجاح",
        description: `تم تحديث ${entity.name}`,
      });
    } catch (error) {
      console.error("Failed to update entity:", error);
      toast({
        title: "خطأ",
        description: "فشل في تحديث العنصر",
        variant: "destructive",
      });
      throw error;
    }
  };

  // حذف عنصر
  const deleteEntity = async (id: number) => {
    try {
      const entityToDelete = entities.find((e) => e.id === id);
      if (!entityToDelete) {
        throw new Error("Entity not found");
      }

      const response = await fetch("/api/entity-name", {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          id, 
          username: currentUser?.name || "Unknown User" 
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete entity");
      }

      setEntities((prev) => prev.filter((e) => e.id !== id));

      addActivity({
        type: "entity",
        description: `تم حذف ${entityToDelete.name}`,
      });

      toast({
        title: "تم بنجاح",
        description: `تم حذف ${entityToDelete.name}`,
      });
    } catch (error) {
      console.error("Failed to delete entity:", error);
      toast({
        title: "خطأ",
        description: "فشل في حذف العنصر",
        variant: "destructive",
      });
      throw error;
    }
  };

  // تحميل البيانات عند بداية التطبيق
  useEffect(() => {
    loadEntities();
  }, []);

  const value = {
    // ... القيم الموجودة
    entities,
    loadEntities,
    addEntity,
    updateEntity,
    deleteEntity,
  };

  return <StoreContext.Provider value={value}>{children}</StoreContext.Provider>;
}
```

---

### **المرحلة 4: إنشاء واجهة المستخدم**

#### **4.1 إنشاء صفحة العرض والإدارة**
```typescript
// app/(main)/entity-name/page.tsx

'use client';

import { useStore } from '@/context/store';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Plus, Edit, Trash2, Search } from 'lucide-react';
import { EntityName } from '@/lib/types';

export default function EntityPage() {
  const { 
    entities, 
    addEntity, 
    updateEntity, 
    deleteEntity, 
    isLoading,
    currentUser 
  } = useStore();

  const [searchTerm, setSearchTerm] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingEntity, setEditingEntity] = useState<EntityName | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    status: 'active',
  });

  // فلترة البيانات حسب البحث
  const filteredEntities = entities.filter((entity) =>
    entity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (entity.description && entity.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // فتح نموذج إضافة جديد
  const handleAdd = () => {
    setEditingEntity(null);
    setFormData({ name: '', description: '', status: 'active' });
    setIsDialogOpen(true);
  };

  // فتح نموذج التعديل
  const handleEdit = (entity: EntityName) => {
    setEditingEntity(entity);
    setFormData({
      name: entity.name,
      description: entity.description || '',
      status: entity.status,
    });
    setIsDialogOpen(true);
  };

  // حفظ البيانات
  const handleSave = async () => {
    try {
      if (editingEntity) {
        // تحديث
        await updateEntity({
          ...editingEntity,
          ...formData,
        });
      } else {
        // إضافة جديد
        await addEntity({
          ...formData,
          userId: currentUser?.id,
        });
      }
      setIsDialogOpen(false);
    } catch (error) {
      // معالجة الأخطاء تتم في Store
    }
  };

  // حذف عنصر
  const handleDelete = async (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
      try {
        await deleteEntity(id);
      } catch (error) {
        // معالجة الأخطاء تتم في Store
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">إدارة العناصر</h1>
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          إضافة جديد
        </Button>
      </div>

      {/* Search and Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي العناصر</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{entities.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">النشطة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {entities.filter(e => e.status === 'active').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <div className="flex items-center space-x-2">
        <Search className="h-4 w-4" />
        <Input
          placeholder="البحث في العناصر..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
      </div>

      {/* Table */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة العناصر</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>الاسم</TableHead>
                <TableHead>الوصف</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>تاريخ الإنشاء</TableHead>
                <TableHead>الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredEntities.length > 0 ? (
                filteredEntities.map((entity) => (
                  <TableRow key={entity.id}>
                    <TableCell className="font-medium">{entity.name}</TableCell>
                    <TableCell>{entity.description || '-'}</TableCell>
                    <TableCell>
                      <Badge variant={entity.status === 'active' ? 'default' : 'secondary'}>
                        {entity.status === 'active' ? 'نشط' : 'غير نشط'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {new Date(entity.createdAt).toLocaleDateString('ar-EG')}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(entity)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(entity.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    لا توجد عناصر مطابقة.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {editingEntity ? 'تعديل العنصر' : 'إضافة عنصر جديد'}
            </DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="name" className="text-right">
                الاسم
              </label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="description" className="text-right">
                الوصف
              </label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              إلغاء
            </Button>
            <Button onClick={handleSave}>
              {editingEntity ? 'تحديث' : 'إضافة'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
```

---

## 🔧 **أمثلة تطبيقية من المشروع:**

### **مثال 1: صفحة المبيعات**
```typescript
// ✅ مطبق بالفعل
API: /api/sales
Model: Sale في Prisma
Functions: addSale, updateSale, deleteSale
UI: app/(main)/sales/page.tsx
```

### **مثال 2: صفحة الصيانة**
```typescript
// ✅ مطبق بالفعل  
API: /api/maintenance-orders, /api/maintenance-receipts
Models: MaintenanceOrder, MaintenanceReceipt
Functions: addMaintenanceOrder, updateMaintenanceReceipt
UI: app/(main)/maintenance/page.tsx
```

### **مثال 3: إعدادات النظام**
```typescript
// ✅ مطبق بالفعل
API: /api/settings
Model: SystemSetting
Functions: updateSystemSettings
UI: app/(main)/settings/page.tsx
```

---

## ⚠️ **نقاط مهمة للتذكر:**

### **🔒 الأمان:**
1. **التحقق من البيانات** - تحقق من صحة البيانات في API
2. **معالجة الأخطاء** - استخدم try/catch blocks
3. **تسجيل الأنشطة** - سجل كل عملية في AuditLog

### **📊 الأداء:**
1. **تحميل البيانات** - استخدم useEffect للتحميل الأولي
2. **التحديث المحلي** - حدث State محلياً بعد API success
3. **Loading States** - أظهر حالة التحميل للمستخدم

### **🎨 واجهة المستخدم:**
1. **Toast Messages** - أظهر رسائل النجاح والفشل
2. **Form Validation** - تحقق من البيانات قبل الإرسال
3. **Responsive Design** - تأكد من توافق الشاشات المختلفة

---

## 📋 **Checklist سريع لأي صفحة جديدة:**

```markdown
☐ إنشاء Model في prisma/schema.prisma
☐ تشغيل npx prisma db push
☐ إضافة Type في lib/types.ts
☐ إنشاء API Route في app/api/[entity]/route.ts
☐ إضافة State في context/store.tsx
☐ إنشاء CRUD Functions في Store
☐ إنشاء UI Component في app/(main)/[page]/page.tsx
☐ إضافة Navigation Link في main-nav.tsx
☐ اختبار جميع العمليات (CRUD)
☐ التأكد من تسجيل الأنشطة
☐ التأكد من معالجة الأخطاء
```

---

## 🎯 **الخلاصة:**

هذا الدليل يوفر **نموذج موحد ومجرب** لربط أي صفحة بقاعدة البيانات. باتباع هذه الخطوات، يمكن تطبيق نفس المنهجية على أي كيان جديد في النظام بسهولة وثبات.

**النتيجة: نظام موحد، قابل للصيانة، وسهل التطوير! 🚀**

---

*تاريخ إنشاء الدليل: 24 يوليو 2025*  
*الإصدار: 1.0*  
*الحالة: جاهز للتطبيق ✅*
