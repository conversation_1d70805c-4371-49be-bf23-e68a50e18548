// Quick test for supply order deletion with proper error handling
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function quickDeleteTest() {
  console.log('🗑️ اختبار سريع للحذف...');
  
  try {
    // Create a test order
    const testOrder = await prisma.supplyOrder.create({
      data: {
        supplyOrderId: `QUICK-DELETE-${Date.now()}`,
        supplierId: 1,
        invoiceNumber: null,
        supplyDate: new Date().toISOString(),
        warehouseId: 1,
        employeeName: 'Quick Test',
        items: JSON.stringify([
          {
            imei: `QUICK-${Date.now()}`,
            manufacturer: 'Test',
            model: 'Quick Model',
            condition: 'جديد'
          }
        ]),
        notes: 'اختبار سريع للحذف',
        status: 'completed'
      }
    });
    
    console.log(`✅ تم إنشاء أمر اختبار: ${testOrder.supplyOrderId}`);
    
    // Test deletion immediately
    const deleteResponse = await fetch('http://localhost:9005/api/supply', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + btoa('user:admin:admin')
      },
      body: JSON.stringify({ id: testOrder.id })
    });
    
    if (deleteResponse.ok) {
      console.log('✅ تم الحذف بنجاح');
    } else {
      const error = await deleteResponse.text();
      console.log('❌ فشل الحذف:', error);
    }
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

quickDeleteTest();
