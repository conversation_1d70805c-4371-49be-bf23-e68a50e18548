// سكريبت لإصلاح تحميل البيانات من APIs بدلاً من localStorage
const fs = require('fs');

const storeFile = 'c:\\Users\\<USER>\\Downloads\\111\\13\\context\\store.tsx';
let content = fs.readFileSync(storeFile, 'utf8');

// إضافة useEffect لتحميل البيانات من APIs
const loadDataFromAPIs = `
  // تحميل البيانات من APIs عند بدء التشغيل
  useEffect(() => {
    const loadDataFromAPIs = async () => {
      try {
        console.log('🔄 جاري تحميل البيانات من APIs...');
        
        // تحميل أوامر التوريد
        try {
          const supplyResponse = await apiClient.get('/api/supply');
          if (supplyResponse.ok) {
            const supplyData = await supplyResponse.json();
            // معالجة items كـ JSON strings
            const processedSupplyOrders = supplyData.map((order: any) => ({
              ...order,
              items: typeof order.items === 'string' ? JSON.parse(order.items) : order.items
            }));
            setSupplyOrders(processedSupplyOrders);
            console.log('✅ تم تحميل أوامر التوريد:', processedSupplyOrders.length);
          }
        } catch (error) {
          console.log('⚠️ فشل تحميل أوامر التوريد:', error);
        }

        // تحميل العملاء
        try {
          const clientsResponse = await apiClient.get('/api/clients');
          if (clientsResponse.ok) {
            const clientsData = await clientsResponse.json();
            setClients(clientsData);
            console.log('✅ تم تحميل العملاء:', clientsData.length);
          }
        } catch (error) {
          console.log('⚠️ فشل تحميل العملاء:', error);
        }

        // تحميل المستخدمين
        try {
          const usersResponse = await apiClient.get('/api/users');
          if (usersResponse.ok) {
            const usersData = await usersResponse.json();
            setUsers(usersData);
            console.log('✅ تم تحميل المستخدمين:', usersData.length);
          }
        } catch (error) {
          console.log('⚠️ فشل تحميل المستخدمين:', error);
        }

        // تحميل المخازن
        try {
          const warehousesResponse = await apiClient.get('/api/warehouses');
          if (warehousesResponse.ok) {
            const warehousesData = await warehousesResponse.json();
            setWarehouses(warehousesData);
            console.log('✅ تم تحميل المخازن:', warehousesData.length);
          }
        } catch (error) {
          console.log('⚠️ فشل تحميل المخازن:', error);
        }

        // تحميل الموردين
        try {
          const suppliersResponse = await apiClient.get('/api/suppliers');
          if (suppliersResponse.ok) {
            const suppliersData = await suppliersResponse.json();
            setSuppliers(suppliersData);
            console.log('✅ تم تحميل الموردين:', suppliersData.length);
          }
        } catch (error) {
          console.log('⚠️ فشل تحميل الموردين:', error);
        }

        console.log('🎉 انتهى تحميل البيانات من APIs');
      } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
      }
    };

    loadDataFromAPIs();
  }, []); // تشغيل مرة واحدة عند التحميل`;

// البحث عن مكان إضافة useEffect
const useEffectPattern = /\/\/ تحميل البيانات عند بدء التشغيل[\s\S]*?}, \[\]\);/;

if (useEffectPattern.test(content)) {
  console.log('✅ تم العثور على useEffect موجود، سيتم الاستبدال...');
  content = content.replace(useEffectPattern, loadDataFromAPIs.trim());
} else {
  console.log('📝 لم يتم العثور على useEffect، سيتم الإضافة...');
  // البحث عن نهاية التعريفات الأساسية وإضافة useEffect
  const insertPoint = /const DeviceFlowProvider[^{]*{/;
  if (insertPoint.test(content)) {
    content = content.replace(insertPoint, (match) => match + loadDataFromAPIs);
  }
}

fs.writeFileSync(storeFile, content, 'utf8');
console.log('✅ تم تحديث store.tsx لتحميل البيانات من APIs');
