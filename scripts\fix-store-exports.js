const fs = require('fs');
const path = require('path');

console.log('🔧 إصلاح store.tsx بشكل كامل...');

try {
  // قراءة الملف
  const filePath = path.join(process.cwd(), 'context', 'store.tsx');
  let content = fs.readFileSync(filePath, 'utf8');
  
  // التحقق من وجود الصادرات
  const storeProviderExport = content.includes('export function StoreProvider');
  const useStoreExport = content.includes('export function useStore');
  
  if (!storeProviderExport || !useStoreExport) {
    console.log('⚠️ لا توجد صادرات في الملف. إعادة كتابة نهاية الملف...');
    
    // إصلاح الأجزاء المهمة في نهاية الملف
    
    // البحث عن بداية StoreProvider
    const storeProviderIndex = content.indexOf('export function StoreProvider');
    if (storeProviderIndex === -1) {
      throw new Error('لم يتم العثور على تعريف StoreProvider');
    }
    
    // البحث عن بداية useStore
    const useStoreIndex = content.indexOf('export function useStore');
    if (useStoreIndex === -1) {
      throw new Error('لم يتم العثور على تعريف useStore');
    }
    
    // نفترض أن التعاريف الأخرى صحيحة، نعيد كتابة نهاية الملف فقط
    let newContent = content.substring(0, useStoreIndex);
    newContent += `export function useStore() {
  const context = useContext(StoreContext);
  if (context === undefined) {
    // Return a safe default instead of throwing error during development
    console.warn(
      "useStore called outside of StoreProvider, returning defaults",
    );

    // Create default permissions with all options enabled
    const createDefaultPermissions = () => {
      const permissions = {} as AppPermissions;
      permissionPages.forEach((page) => {
        permissions[page] = {
          view: true,
          create: true,
          update: true,
          delete: true,
        };
      });
      return permissions;
    };

    return {
      isLoading: false,
      devices: [],
      clients: [],
      suppliers: [],
      warehouses: [],
      sales: [],
      returns: [],
      activities: [],
      deviceReturnHistory: [],
      manufacturers: [],
      deviceModels: [],
      supplyOrders: [],
      settings: {
        maintenanceAreas: [],
        departments: [],
        jobTitles: [],
        categories: [],
        supplierTypes: [],
        businessTypes: [],
        jobPriorities: [],
        defectTypes: [],
        stocktakeFrequencies: []
      },
      employeeRequests: [],
      maintenanceOrders: [],
      permissions: createDefaultPermissions(),
      user: null,
      setUser: () => {},
      login: () => Promise.resolve(false),
      logout: () => {},
      addDevice: () => Promise.resolve(""),
      updateDevice: () => Promise.resolve(false),
      deleteDevice: () => Promise.resolve(false),
      addClient: () => Promise.resolve(""),
      updateClient: () => Promise.resolve(false),
      deleteClient: () => Promise.resolve(false),
      addSupplier: () => Promise.resolve(""),
      updateSupplier: () => Promise.resolve(false),
      deleteSupplier: () => Promise.resolve(false),
      addWarehouse: () => Promise.resolve(""),
      updateWarehouse: () => Promise.resolve(false),
      deleteWarehouse: () => Promise.resolve(false),
      addSale: () => Promise.resolve(""),
      updateSale: () => Promise.resolve(false),
      deleteSale: () => Promise.resolve(false),
      addManufacturer: () => Promise.resolve(""),
      updateManufacturer: () => Promise.resolve(false),
      deleteManufacturer: () => Promise.resolve(false),
      addDeviceModel: () => Promise.resolve(""),
      updateDeviceModel: () => Promise.resolve(false),
      deleteDeviceModel: () => Promise.resolve(false),
      addSupplyOrder: () => Promise.resolve(""),
      updateSupplyOrder: () => Promise.resolve(false),
      deleteSupplyOrder: () => Promise.resolve(false),
      addReturn: () => Promise.resolve(""),
      updateReturn: () => Promise.resolve(false),
      deleteReturn: () => Promise.resolve(false),
      addActivity: () => Promise.resolve(""),
      updateActivity: () => Promise.resolve(false),
      deleteActivity: () => Promise.resolve(false),
      addDeviceReturn: () => Promise.resolve(""),
      updateDeviceReturn: () => Promise.resolve(false),
      deleteDeviceReturn: () => Promise.resolve(false),
      getFilteredDevices: () => [],
      getFilteredClients: () => [],
      getFilteredSuppliers: () => [],
      getFilteredWarehouses: () => [],
      getFilteredSales: () => [],
      getFilteredManufacturers: () => [],
      getFilteredDeviceModels: () => [],
      getFilteredSupplyOrders: () => [],
      getFilteredReturns: () => [],
      getFilteredActivities: () => [],
      getFilteredDeviceReturns: () => [],
      getFilteredEmployeeRequests: () => [],
      getFilteredMaintenanceOrders: () => [],
      addEmployeeRequest: () => Promise.resolve(""),
      updateEmployeeRequest: () => Promise.resolve(false),
      deleteEmployeeRequest: () => Promise.resolve(false),
      addMaintenanceOrder: () => Promise.resolve(""),
      updateMaintenanceOrder: () => Promise.resolve(false),
      deleteMaintenanceOrder: () => Promise.resolve(false),
      changeMaintenanceOrderStatus: () => Promise.resolve(false),
      assignTechnician: () => Promise.resolve(false),
      addSetting: () => Promise.resolve(""),
      updateSetting: () => Promise.resolve(false),
      deleteSetting: () => Promise.resolve(false),
      updateSettings: () => Promise.resolve(false),
      addStocktake: () => Promise.resolve(""),
      updateStocktake: () => Promise.resolve(false),
      deleteStocktake: () => Promise.resolve(false),
      getFilteredStocktakes: () => [],
      addStocktakeItem: () => {},
      updateStocktakeItem: () => {},
      addStocktakeDiscrepancy: () => {},
      resolveStocktakeDiscrepancy: () => {},
      changeStocktakeStatus: () => {},
      reviewStocktake: () => {},
      createBackupSnapshot: () => ({}),
      restoreFromSnapshot: () => {},
      exportStoreData: () => ({}),
      importStoreData: () => {},
    };
  }
  return context;
}`;

    // كتابة الملف المحدث
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log('✅ تم إصلاح الملف بنجاح!');
  } else {
    console.log('✅ الصادرات موجودة، لا حاجة للإصلاح.');
  }

  // التحقق من توازن الأقواس
  const openBraces = (content.match(/\{/g) || []).length;
  const closeBraces = (content.match(/\}/g) || []).length;
  
  console.log(`- أقواس فتح: ${openBraces}`);
  console.log(`- أقواس إغلاق: ${closeBraces}`);
  
  if (openBraces === closeBraces) {
    console.log('✅ الأقواس متوازنة!');
  } else {
    console.log(`❌ الأقواس غير متوازنة! الفرق: ${openBraces - closeBraces}`);
    
    if (openBraces > closeBraces) {
      console.log(`نحتاج إلى إضافة ${openBraces - closeBraces} قوس إغلاق}`);
    } else {
      console.log(`نحتاج إلى إزالة ${closeBraces - openBraces} قوس إغلاق}`);
    }
  }
  
} catch (error) {
  console.error('❌ حدث خطأ:', error.message);
}
